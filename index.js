require('dotenv').config({ path: './sfrr-main/.env' });
const { token } = process.env;
const { Client, Collection, GatewayIntentBits } = require("discord.js");
const fs = require("fs");
const { REST } = require("@discordjs/rest");
const { Routes } = require("discord-api-types/v10");
const path = require('path');
const { initializeEconomy } = require('./utils/economy');
const mongoose = require('mongoose');
const mongoURL = process.env.mongoURL;

// Connect to MongoDB
const connectToMongoDB = async () => {
  try {
    await mongoose.connect(mongoURL);
    console.log('✅ Connected to MongoDB database');
    return true;
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    return false;
  }
};

// Create MongoDB models
const createModels = () => {
  // Vehicle model
  const vehicleSchema = new mongoose.Schema({
    userId: { type: String, required: true },
    vehicles: [{ 
      make: String,
      model: String,
      year: String,
      color: String,
      licensePlate: String,
      registrationDate: { type: Date, default: Date.now }
    }]
  });
  
  // Ticket model
  const ticketSchema = new mongoose.Schema({
    userId: { type: String, required: true },
    tickets: [{
      reason: String,
      date: { type: Date, default: Date.now },
      officer: String,
      fine: Number
    }]
  });
  
  // Economy model
  const economySchema = new mongoose.Schema({
    userId: { type: String, required: true, unique: true },
    balance: { type: Number, default: 0 }
  });

  // Session Feedback model
  const sessionFeedbackSchema = new mongoose.Schema({
    userId: { type: String, required: true },
    rating: { type: Number, required: true },
    improvement: { type: String, required: true },
    notes: String,
    timestamp: { type: Date, default: Date.now }
  });

  // Moderation Log model
  const modLogSchema = new mongoose.Schema({
    guildId: { type: String, required: true },
    targetUserId: { type: String, required: true },
    moderatorId: { type: String, required: true },
    action: {
      type: String,
      required: true,
      enum: ['ban', 'unban', 'kick', 'warn', 'mute', 'unmute', 'timeout', 'untimeout']
    },
    reason: { type: String, required: true },
    proof: { type: String, default: null },
    duration: { type: String, default: null }, // For temporary actions
    timestamp: { type: Date, default: Date.now },
    caseId: { type: Number, required: true }
  });

  // Create and attach models to client
  client.models = {
    Vehicle: mongoose.model('Vehicle', vehicleSchema),
    Ticket: mongoose.model('Ticket', ticketSchema),
    Economy: mongoose.model('Economy', economySchema),
    SessionFeedback: mongoose.model('SessionFeedback', sessionFeedbackSchema),
    ModLog: mongoose.model('ModLog', modLogSchema)
  };

  console.log('✅ MongoDB models created');
};

global.tictactoeGames = new Map();

const client = new Client({ 
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.GuildMessageReactions,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildPresences
  ],
  partials: ['MESSAGE', 'CHANNEL', 'REACTION']
});

// Set max listeners for the emitter
const { AsyncEventEmitter } = require('@vladfrangu/async_event_emitter');
AsyncEventEmitter.defaultMaxListeners = 25;

client.commands = new Collection();
client.commandArray = [];

const clientId = "1386832048865415310"; // Your bot's client ID
const guildId = "1310346706285625434";  // Your Discord server (guild) ID

// Load vehicle data from MongoDB
const loadVehicleData = async () => {
  try {
    client.vehicleData = {};
    const vehicles = await client.models.Vehicle.find({});
    
    for (const vehicle of vehicles) {
      client.vehicleData[vehicle.userId] = vehicle.vehicles;
    }
    
    console.log('✅ Vehicle data loaded from MongoDB successfully');
  } catch (error) {
    console.error('❌ Error loading vehicle data from MongoDB:', error);
    
    // Fallback to JSON if MongoDB fails
    const vehicleDataPath = path.join(__dirname, 'data', 'vehicleData');
    client.vehicleData = {};

    if (!fs.existsSync(vehicleDataPath)) {
      fs.mkdirSync(vehicleDataPath, { recursive: true });
      return;
    }

    const files = fs.readdirSync(vehicleDataPath).filter(file => file.endsWith('.json'));
    for (const file of files) {
      try {
        const userId = file.replace('.json', '');
        const data = fs.readFileSync(path.join(vehicleDataPath, file), 'utf8');
        client.vehicleData[userId] = JSON.parse(data);
      } catch (error) {
        console.error(`Error loading vehicle data from ${file}:`, error);
      }
    }
    console.log('✅ Vehicle data loaded from JSON successfully (MongoDB fallback)');
  }
};

// Load tickets data from MongoDB
const loadTicketsData = async () => {
  try {
    client.ticketsData = {};
    const tickets = await client.models.Ticket.find({});
    
    for (const ticket of tickets) {
      client.ticketsData[ticket.userId] = ticket.tickets;
    }
    
    console.log('✅ Tickets data loaded from MongoDB successfully');
  } catch (error) {
    console.error('❌ Error loading tickets data from MongoDB:', error);
    
    // Fallback to JSON if MongoDB fails
    const ticketsDataPath = path.join(__dirname, 'data', 'tickets');
    client.ticketsData = {};

    if (!fs.existsSync(ticketsDataPath)) {
      fs.mkdirSync(ticketsDataPath, { recursive: true });
      return;
    }

    const files = fs.readdirSync(ticketsDataPath).filter(file => file.endsWith('.json'));
    for (const file of files) {
      try {
        const userId = file.replace('.json', '');
        const data = fs.readFileSync(path.join(ticketsDataPath, file), 'utf8');
        client.ticketsData[userId] = JSON.parse(data);
      } catch (error) {
        console.error(`Error loading tickets data from ${file}:`, error);
      }
    }
    console.log('✅ Tickets data loaded from JSON successfully (MongoDB fallback)');
  }
};

// Save vehicle data to MongoDB
client.saveVehicleData = async (userId) => {
  try {
    if (!client.vehicleData[userId]) return;
    
    await client.models.Vehicle.findOneAndUpdate(
      { userId },
      { userId, vehicles: client.vehicleData[userId] },
      { upsert: true, new: true }
    );
    
    console.log(`Vehicle data saved to MongoDB for user ${userId}`);
  } catch (error) {
    console.error(`Error saving vehicle data to MongoDB for user ${userId}:`, error);
    
    // Fallback to JSON if MongoDB fails
    const vehicleDataPath = path.join(__dirname, 'data', 'vehicleData');
    if (!fs.existsSync(vehicleDataPath)) {
      fs.mkdirSync(vehicleDataPath, { recursive: true });
    }
    
    fs.writeFileSync(
      path.join(vehicleDataPath, `${userId}.json`),
      JSON.stringify(client.vehicleData[userId], null, 2)
    );
    console.log(`Vehicle data saved to JSON for user ${userId} (MongoDB fallback)`);
  }
};

// Save tickets data to MongoDB
client.saveTicketData = async (userId) => {
  try {
    if (!client.ticketsData[userId]) return;
    
    await client.models.Ticket.findOneAndUpdate(
      { userId },
      { userId, tickets: client.ticketsData[userId] },
      { upsert: true, new: true }
    );
    
    console.log(`Ticket data saved to MongoDB for user ${userId}`);
  } catch (error) {
    console.error(`Error saving ticket data to MongoDB for user ${userId}:`, error);
    
    // Fallback to JSON if MongoDB fails
    const ticketsDataPath = path.join(__dirname, 'data', 'tickets');
    if (!fs.existsSync(ticketsDataPath)) {
      fs.mkdirSync(ticketsDataPath, { recursive: true });
    }
    
    fs.writeFileSync(
      path.join(ticketsDataPath, `${userId}.json`),
      JSON.stringify(client.ticketsData[userId], null, 2)
    );
    console.log(`Ticket data saved to JSON for user ${userId} (MongoDB fallback)`);
  }
};

// Save tickets data to JSON
client.saveTicketData = async (userId) => {
  try {
    if (!client.ticketsData[userId]) return;
    
    const ticketsDataPath = path.join(__dirname, 'data', 'tickets');
    if (!fs.existsSync(ticketsDataPath)) {
      fs.mkdirSync(ticketsDataPath, { recursive: true });
    }
    
    fs.writeFileSync(
      path.join(ticketsDataPath, `${userId}.json`),
      JSON.stringify(client.ticketsData[userId], null, 2)
    );
    
    console.log(`Ticket data saved for user ${userId}`);
  } catch (error) {
    console.error(`Error saving ticket data for user ${userId}:`, error);
  }
};

// Handle Events
const handleEvents = async () => {
  const eventFiles = fs.readdirSync(`./events`).filter((file) => file.endsWith(".js"));
  
  // Create a collection for active components
  client.activeComponents = new Collection();
  
  for (const file of eventFiles) {
    try {
      const eventModule = require(`./events/${file}`);
      
      if (Array.isArray(eventModule)) {
        eventModule.forEach(event => {
          if (event.once) {
            client.once(event.name, (...args) => event.execute(...args, client));
          } else {
            client.on(event.name, (...args) => event.execute(...args, client));
          }
          console.log(`✅ Loaded event from array: ${event.name}`);
        });
      } else {
        const event = eventModule;
        if (event.once) {
          client.once(event.name, (...args) => event.execute(...args, client));
        } else {
          client.on(event.name, (...args) => event.execute(...args, client));
        }
        console.log(`✅ Loaded event: ${event.name}`);
      }
    } catch (error) {
      console.error(`❌ Error loading event ${file}:`, error);
    }
  }

  // Add specific interaction handling
  client.on('interactionCreate', async (interaction) => {
    try {
      if (interaction.isButton()) {
        const handler = client.activeComponents.get(interaction.customId);
        if (handler) {
          await handler(interaction);
        }
      }
    } catch (error) {
      console.error('Error handling interaction:', error);
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: 'There was an error processing your interaction.',
            ephemeral: true
          });
        }
      } catch (replyError) {
        console.error('Error sending error response:', replyError);
      }
    }
  });
};

// Handle Commands
const loadCommands = async () => {
  const commandFolders = fs.readdirSync("./commands");
  const commandNames = new Set(); // Track command names

  client.commands = new Collection();
  client.commandArray = [];

  console.log('Starting to load commands...');
  console.log(`Found ${commandFolders.length} command folders/files`);

  for (const folder of commandFolders) {
    const folderPath = path.join(__dirname, "commands", folder);
    
    // Check if it's a directory or a file
    const stats = fs.statSync(folderPath);
    if (stats.isDirectory()) {
      console.log(`Processing directory: ${folder}`);
      const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith(".js"));
      console.log(`Found ${commandFiles.length} command files in ${folder}`);
      
      for (const file of commandFiles) {
        try {
          delete require.cache[require.resolve(`${folderPath}/${file}`)];
          const command = require(`${folderPath}/${file}`);
          
          if (!command.data || typeof command.data.toJSON !== "function") {
            console.error(`Command file ${folder}/${file} is missing required properties`);
            continue;
          }
          
          const commandName = command.data.name;
          if (commandNames.has(commandName)) {
            console.error(`Duplicate command name found: ${commandName} in ${folder}/${file}`);
            continue;
          }
          
          commandNames.add(commandName);
          client.commands.set(commandName, command);
          client.commandArray.push(command.data.toJSON());
          console.log(`✅ Loaded command: ${commandName} from ${folder}/${file}`);
        } catch (error) {
          console.error(`❌ Error loading command file ${folder}/${file}:`, error);
        }
      }
    } else if (folder.endsWith(".js")) {
      try {
        delete require.cache[require.resolve(folderPath)];
        const command = require(folderPath);
        
        if (!command.data || typeof command.data.toJSON !== "function") {
          console.error(`Command file ${folder} is missing required properties`);
          continue;
        }

        const commandName = command.data.name;
        if (commandNames.has(commandName)) {
          console.error(`Duplicate command name found: ${commandName} in ${folder}`);
          continue;
        }

        commandNames.add(commandName);
        client.commands.set(commandName, command);
        client.commandArray.push(command.data.toJSON());
        console.log(`✅ Loaded command: ${commandName} from ${folder}`);
      } catch (error) {
        console.error(`❌ Error loading command file ${folder}:`, error);
      }
    }
  }

  console.log(`Total commands loaded: ${client.commandArray.length}`);

  // Register Commands with Discord
  const rest = new REST({ version: "10" }).setToken(token);
  try {
    console.log('Started refreshing application (/) commands...');
    
    await rest.put(
      Routes.applicationGuildCommands(clientId, guildId),
      { body: client.commandArray },
    );

    console.log("✅ Slash commands registered successfully.");
  } catch (error) {
    console.error("❌ Error registering commands:", error);
    console.error(error);
  }
};

// Migrate existing JSON data to MongoDB
const migrateDataToMongoDB = async () => {
  try {
    console.log('Starting data migration to MongoDB...');
    
    // Migrate vehicle data
    const vehicleDataPath = path.join(__dirname, 'data', 'vehicleData');
    if (fs.existsSync(vehicleDataPath)) {
      const files = fs.readdirSync(vehicleDataPath).filter(file => file.endsWith('.json'));
      for (const file of files) {
        try {
          const userId = file.replace('.json', '');
          const data = fs.readFileSync(path.join(vehicleDataPath, file), 'utf8');
          const vehicles = JSON.parse(data);
          
          await client.models.Vehicle.findOneAndUpdate(
            { userId },
            { userId, vehicles },
            { upsert: true }
          );
        } catch (error) {
          console.error(`Error migrating vehicle data from ${file}:`, error);
        }
      }
    }
    
    // Migrate tickets data
    const ticketsDataPath = path.join(__dirname, 'data', 'tickets');
    if (fs.existsSync(ticketsDataPath)) {
      const files = fs.readdirSync(ticketsDataPath).filter(file => file.endsWith('.json'));
      for (const file of files) {
        try {
          const userId = file.replace('.json', '');
          const data = fs.readFileSync(path.join(ticketsDataPath, file), 'utf8');
          const tickets = JSON.parse(data);
          
          await client.models.Ticket.findOneAndUpdate(
            { userId },
            { userId, tickets },
            { upsert: true }
          );
        } catch (error) {
          console.error(`Error migrating tickets data from ${file}:`, error);
        }
      }
    }
    
    console.log('✅ Data migration to MongoDB completed');
  } catch (error) {
    console.error('❌ Error during data migration:', error);
  }
};

// Initialize the bot
(async () => {
  try {
    // Connect to MongoDB first
    const mongoConnected = await connectToMongoDB();
    
    // Create MongoDB models regardless of connection status
    createModels();
    
    if (mongoConnected) {
      // Migrate existing data to MongoDB (only needed once)
      await migrateDataToMongoDB();
      
      // Load data from MongoDB
      await loadVehicleData();
      await loadTicketsData();
    } else {
      // Fallback to JSON if MongoDB connection fails
      console.log('⚠️ Using JSON storage as fallback');
      await loadVehicleData();
      await loadTicketsData();
    }
    
    await handleEvents(); // Load events
    await loadCommands(); // Register commands
    await client.login(token); // Log in to Discord
    await initializeEconomy();
    global.cooldowns = new Map();
  } catch (error) {
    console.error("Error during bot initialization:", error);
  }
})();

// Add error handling for uncaught exceptions
process.on('unhandledRejection', (error) => {
  console.error('Unhandled promise rejection:', error);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  // Attempt to gracefully handle the error
  try {
    // Log the error to a file or monitoring service
    fs.appendFileSync('error.log', `${new Date().toISOString()} - ${error}\n`);
    
    // If the error is critical, attempt to restart the bot
    if (error.message.includes('ECONNRESET') || error.message.includes('CONNECTION_CLOSED')) {
      console.log('Attempting to reconnect...');
      client.destroy();
      client.login(token);
    }
  } catch (logError) {
    console.error('Failed to handle error:', logError);
  }
});

// Add this function to save session feedback
client.saveSessionFeedback = async (userId, feedback) => {
  try {
    const newFeedback = new client.models.SessionFeedback({
      userId,
      ...feedback
    });
    await newFeedback.save();
    console.log(`Session feedback saved for user ${userId}`);
  } catch (error) {
    console.error(`Error saving session feedback for user ${userId}:`, error);
  }
};

// Add this to handle Discord API rate limits
client.on('rateLimit', (rateLimitInfo) => {
  console.warn('Rate limit hit:', rateLimitInfo);
});

// Add this to handle WebSocket errors
client.ws.on('error', (error) => {
  console.error('WebSocket error:', error);
  // Attempt to reconnect
  setTimeout(() => {
    client.destroy();
    client.login(token);
  }, 5000);
});

// Modify your client error handling
client.on('error', (error) => {
  console.error('Client error:', error);
});

client.on('shardError', (error) => {
  console.error('WebSocket error:', error);
});
