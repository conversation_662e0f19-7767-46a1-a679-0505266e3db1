const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, MessageFlags } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Path for JSON storage
const jsonFilePath = path.join(__dirname, '..', '..', 'data', 'earlyAccessLinks.json');

// Function to ensure the JSON file exists
function initializeJsonFile() {
    try {
        // Create data directory if it doesn't exist
        const dataDir = path.join(__dirname, '..', '..', 'data');
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        // Create JSON file if it doesn't exist
        if (!fs.existsSync(jsonFilePath)) {
            fs.writeFileSync(jsonFilePath, JSON.stringify({}, null, 2), 'utf8');
            console.log('Created earlyAccessLinks.json file');
        }
    } catch (error) {
        console.error('Error initializing JSON file:', error);
    }
}

// Initialize the JSON file when the command is loaded
initializeJsonFile();

module.exports = {
    data: new SlashCommandBuilder()
        .setName('earlyaccess')
        .setDescription('Grant early access to a user with a link')
        .addStringOption(option =>
            option.setName('link')
                .setDescription('The link for early access')
                .setRequired(true)),

    async execute(interaction) {
        try {
            const staffRoleId = '1362775746329841796';

            if (!interaction.member.roles.cache.has(staffRoleId)) {
                return await interaction.reply({
                    content: 'You do not have permission to execute this command.',
                    flags: MessageFlags.Ephemeral
                });
            }

            await interaction.reply({ content: 'Early access released!', ephemeral: true });

            const link = interaction.options.getString('link');

            const embed = new EmbedBuilder()
                .setTitle('Early Access')
                .setDescription(`> The **Early Access** link has been released! Please find the link below to join and gain early access to the session.`)
                .setColor('#626CE3')
                .setThumbnail('https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&')
                .setFooter({
                    text: 'Greenville Roleplay Corporation',
                    iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
                })
                .setTimestamp();

            const button = new ButtonBuilder()
                .setLabel('Early Access')
                .setStyle(ButtonStyle.Primary)
                .setCustomId('early_access_link');

            const row = new ActionRowBuilder().addComponents(button);

            const message = await interaction.channel.send({
                content: `@here`, // Pings the role at the top of the message
                embeds: [embed],
                components: [row]
            });

            // Ensure JSON file exists before writing
            initializeJsonFile();

            // Store the link in JSON file
            try {
                let links = {};
                const data = fs.readFileSync(jsonFilePath, 'utf8');
                links = JSON.parse(data);
                
                links[message.id] = link;
                fs.writeFileSync(jsonFilePath, JSON.stringify(links, null, 2));
            } catch (error) {
                console.error('Error storing link in JSON:', error);
            }

            // Log the command execution
            const logChannelId = '1381460621744406629'; // Replace with your actual log channel ID
            const logChannel = interaction.guild.channels.cache.get(logChannelId);
            if (logChannel) {
                const logEmbed = new EmbedBuilder()
                    .setTitle('Command Executed')
                    .setDescription('The `/earlyaccess` command was executed.')
                    .addFields(
                        { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
                        { name: 'User ID', value: `${interaction.user.id}`, inline: true },
                        { name: 'Channel', value: `${interaction.channel.name}`, inline: true },
                        { name: 'Link Provided', value: `${link}`, inline: false },
                    )
                    .setColor('#02a0dd')
                    .setTimestamp();

                logChannel.send({ embeds: [logEmbed] });
            }
        } catch (error) {
            console.error('Error in earlyaccess command:', error);
            await interaction.followUp({
                content: 'There was an error while executing this command!',
                ephemeral: true
            }).catch(console.error);
        }
    }
};
