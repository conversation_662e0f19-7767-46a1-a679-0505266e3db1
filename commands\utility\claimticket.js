const { SlashCommandBuilder, PermissionsBitField } = require('discord.js');
const { EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('claimticket')
        .setDescription('Claim a ticket'),
    
    async execute(interaction) {
        // Defer the reply immediately
        await interaction.deferReply({ ephemeral: true });

        const supportRoleId = '1363682766855606272'; // Role that can use the command
        const seniorStaffRoleId = '1363386638490927296'; // Role that can always view/talk in tickets

        // Check if user has the support role
        if (!interaction.member.roles.cache.has(supportRoleId)) {
            await interaction.editReply({ 
                content: 'You do not have permission to claim tickets.'
            });
            return;
        }

        // Check if this is a ticket channel
        if (!interaction.channel.name.startsWith('staff-support-') && 
            !interaction.channel.name.startsWith('member-report-') && 
            !interaction.channel.name.startsWith('partnership-')) {
            await interaction.editReply({ 
                content: 'This is not a ticket!'
            });
            return;
        }

        // Check if ticket is already claimed
        const messages = await interaction.channel.messages.fetch();
        const claimMessage = messages.find(msg => 
            msg.content.includes('has claimed this ticket')
        );

        if (claimMessage) {
            const claimedBy = claimMessage.content.split(' has claimed')[0];
            await interaction.editReply({ 
                content: `This ticket has already been claimed by ${claimedBy}`
            });
            return;
        }

        // Get the first message to find the ticket creator
        const firstMessage = messages.last(); // Get the oldest message
        const ticketCreator = firstMessage?.embeds[0]?.description?.match(/<@(\d+)>/)?.[1];

        if (!ticketCreator) {
            await interaction.editReply({ 
                content: 'Could not determine ticket creator. Please contact an administrator.'
            });
            return;
        }

        // Update channel permissions
        await interaction.channel.permissionOverwrites.set([
            // Default permissions (everyone)
            {
                id: interaction.guild.id,
                deny: [PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ViewChannel],
            },
            // Support role permissions (can only view)
            {
                id: supportRoleId,
                allow: [PermissionsBitField.Flags.ViewChannel],
                deny: [PermissionsBitField.Flags.SendMessages],
            },
            // Ticket claimer permissions
            {
                id: interaction.user.id,
                allow: [PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ViewChannel],
            },
            // Senior staff permissions (can always view/talk)
            {
                id: seniorStaffRoleId,
                allow: [PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ViewChannel],
            },
            // Ticket creator permissions
            {
                id: ticketCreator,
                allow: [PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ViewChannel],
            }
        ]);

        // Claim the ticket
        await interaction.channel.send({
            content: `<@${interaction.user.id}> has claimed this ticket.`
        });

        await interaction.editReply({ 
            content: 'You have successfully claimed this ticket.'
        });
    },
};
