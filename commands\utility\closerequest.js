const { 
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
    <PERSON><PERSON><PERSON><PERSON><PERSON>, 
    ActionRowBuilder, 
    ButtonBuilder, 
    ButtonStyle 
} = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('closerequest')
        .setDescription('Request to close a ticket'),

    async execute(interaction) {
        const supportRoleId = '1363682766855606272'; // Role that can use the command

        // Check if user has the support role
        if (!interaction.member.roles.cache.has(supportRoleId)) {
            await interaction.reply({ 
                content: 'You do not have permission to request ticket closure.', 
                ephemeral: true 
            });
            return;
        }

        // Check if this is a ticket channel
        if (!interaction.channel.name.startsWith('staff-support-') && 
            !interaction.channel.name.startsWith('member-report-') && 
            !interaction.channel.name.startsWith('partnership-')) {
            await interaction.reply({ 
                content: 'This is not a ticket!', 
                ephemeral: true 
            });
            return;
        }

        // Get the first message to find the ticket creator
        const messages = await interaction.channel.messages.fetch({ limit: 100 });
        const firstMessage = messages.last(); // Get the oldest message
        
        // Look for the mentioned user in the first message's embeds
        const ticketCreator = firstMessage.embeds[0]?.description?.match(/<@(\d+)>/)?.[1];

        if (!ticketCreator) {
            await interaction.reply({
                content: 'Could not determine ticket creator. Please close the ticket manually.',
                ephemeral: true
            });
            return;
        }

        // Check if there's already a close request
        const existingCloseRequest = Array.from((await interaction.channel.messages.fetch()).values())
            .find(msg => msg.embeds[0]?.title === 'Ticket Close Request');

        if (existingCloseRequest) {
            await interaction.reply({
                content: 'There is already an active close request for this ticket.',
                ephemeral: true
            });
            return;
        }

        const closeRequestEmbed = new EmbedBuilder()
            .setColor('#FF6B6B')
            .setTitle('Ticket Close Request')
            .setDescription(`<@${interaction.user.id}> is requesting to close this ticket.\n\nIf you fail to choose an option within 24 hours, this ticket will be automatically closed by staff.`)
            .setTimestamp()
            .setFooter({ text: 'Request will expire in 24 hours' });

        const buttons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`close_request_accept_${ticketCreator}`)
                    .setLabel('Close')
                    .setStyle(ButtonStyle.Danger),
                new ButtonBuilder()
                    .setCustomId(`close_request_deny_${ticketCreator}`)
                    .setLabel('Deny')
                    .setStyle(ButtonStyle.Secondary)
            );

        const closeRequestMessage = await interaction.channel.send({
            content: `<@${ticketCreator}>`,
            embeds: [closeRequestEmbed],
            components: [buttons]
        });

        // Set up auto-close after 24 hours
        setTimeout(async () => {
            try {
                const message = await interaction.channel.messages.fetch(closeRequestMessage.id);
                if (message) {
                    // Check if the message still exists and hasn't been handled
                    const embed = message.embeds[0];
                    if (embed && embed.title === 'Ticket Close Request') {
                        await message.delete();
                        await interaction.channel.send({
                            content: 'Close request has expired. Ticket will now be closed automatically.',
                            embeds: [
                                new EmbedBuilder()
                                    .setColor('#FF0000')
                                    .setTitle('Ticket Auto-Closed')
                                    .setDescription('This ticket was automatically closed due to an expired close request.')
                                    .setTimestamp()
                            ]
                        });
                        // Add small delay before closing
                        setTimeout(() => interaction.channel.delete(), 5000);
                    }
                }
            } catch (error) {
                console.error('Error handling auto-close:', error);
            }
        }, 24 * 60 * 60 * 1000); // 24 hours

        await interaction.reply({
            content: 'Close request has been sent.',
            ephemeral: true
        });
    },
};




