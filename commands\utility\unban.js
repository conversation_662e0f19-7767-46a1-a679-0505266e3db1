const {
  SlashCommandBuilder,
  PermissionFlagsBits,
  EmbedBuilder
} = require('discord.js');
const { saveModLogEntry } = require('./modlog.js');

const allowedRoleIds = ['1314078903522037802', '1374162168408309931'];
const logChannelId = '1384018163330715700';

module.exports = {
  data: new SlashCommandBuilder()
    .setName('unban')
    .setDescription('Unban a user from the server.')
    .addStringOption(option =>
      option.setName('userid')
        .setDescription('The ID of the user to unban.')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('reason')
        .setDescription('Reason for the unban.')
        .setRequired(false))
    .setDefaultMemberPermissions(PermissionFlagsBits.BanMembers),

  async execute(interaction) {
    await interaction.deferReply({ ephemeral: true });

    const userId = interaction.options.getString('userid');
    const reason = interaction.options.getString('reason') || 'No reason provided';
    const executor = interaction.user;

    const memberRoles = interaction.member.roles.cache;
    const hasPermission = allowedRoleIds.some(roleId => memberRoles.has(roleId));

    if (!hasPermission) {
      return interaction.editReply({
        content: 'You do not have permission to use this command.'
      });
    }

    let bannedUser;
    try {
      bannedUser = await interaction.client.users.fetch(userId);
      await interaction.guild.bans.fetch(userId);
    } catch {
      return interaction.editReply({
        content: 'That user is not banned or the ID is invalid.'
      });
    }

    // DM Embed
    const dmEmbed = new EmbedBuilder()
      .setTitle('You Have Been Unbanned From Greenville Roleplay Corporation')
      .setColor('#626CE3')
      .setDescription(`You have been unbanned from **${interaction.guild.name}** by **${executor.tag}**.`)
      .addFields({ name: 'Reason', value: reason })
      .setFooter({ text: 'If you have any questions, please contact one of the HRS.' });

    try {
      await bannedUser.send({ embeds: [dmEmbed] });
    } catch {
      // Ignore if DM fails
    }

    try {
      await interaction.guild.members.unban(userId, reason);

      // Log the unban action to modlog
      const caseId = saveModLogEntry({
        guildId: interaction.guild.id,
        targetUserId: userId,
        moderatorId: executor.id,
        action: 'unban',
        reason: reason,
        proof: null
      });

      const logchannelEmbed = new EmbedBuilder()
        .setTitle('User Unbanned')
        .setColor('#626CE3')
        .addFields(
          { name: 'User ID', value: userId },
          { name: 'Reason', value: reason },
          { name: 'Case ID', value: `#${caseId}`, inline: true }
        )
        .setFooter({ text: `Unbanned by ${executor.tag}` });

      // Send to log channel
      try {
        const logChannel = interaction.guild.channels.cache.get(logChannelId);
        if (logChannel) {
          await logChannel.send({ embeds: [logchannelEmbed] });
        }
      } catch (error) {
        console.error('Failed to send unban log to channel:', error);
      }

      const confirmationEmbed = new EmbedBuilder()
        .setTitle('User Unbanned Successfully')
        .setColor('#626CE3')
        .addFields(
          { name: 'User ID', value: userId },
          { name: 'Reason', value: reason },
          { name: 'Case ID', value: `#${caseId}`, inline: true }
        )
        .setFooter({ text: `Unbanned by ${executor.tag}` });

      return interaction.editReply({ embeds: [confirmationEmbed] });

    } catch {
      return interaction.editReply({
        content: 'Failed to unban the user. Please check if the ID is valid and I have the required permissions.'
      });
    }
  },
};
