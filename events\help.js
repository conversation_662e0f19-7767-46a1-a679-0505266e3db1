const { Events, EmbedBuilder } = require('discord.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isStringSelectMenu()) return;

        if (interaction.customId === 'help_options') {
            await handleHelpDropdown(interaction);
        }
    },
};

async function handleHelpDropdown(interaction) {
    let embed;

    switch (interaction.values[0]) {
        case 'register_vehicle':
            embed = new EmbedBuilder()
                .setTitle('How can I Register my Vehicle?')
                .setDescription('To register your vehicle, use our Discord Bot and head over to the <#1314079268011249694> channel.')
                .setColor('#626CE3')
                .setFooter({
                    text: 'Greenville Roleplay Corporation',
                    iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
                });
            break;

        case 'join_swfl_session':
            embed = new EmbedBuilder()
                .setTitle('How do I join GV Session?')
                .setDescription('To join our Greenville Roleplay sessions, watch for pings in https://discord.com/channels/1310346706285625434/1314079230203527178.')
                .setColor('#626CE3')
                .setFooter({
                    text: 'Greenville Roleplay Corporation',
                    iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
                });
            break;

        case 'become_staff_ps_member':
            embed = new EmbedBuilder()
                .setTitle('How can I be a P/S Member?')
                .setDescription('To become a P/S Member, check out the https://discord.com/channels/1310346706285625434/1314079204953817099 channel and look for the applications available via dropdown.')
                .setColor('#626CE3')
                .setFooter({
                    text: 'Greenville Roleplay Corporation',
                    iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
                });
            break;

        default:
            embed = new EmbedBuilder()
                .setDescription('Unknown option selected.')
                .setColor('#ff0000');
            break;
    }

    await interaction.reply({ embeds: [embed], ephemeral: true });
}
