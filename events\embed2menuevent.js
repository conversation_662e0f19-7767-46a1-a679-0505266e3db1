const { Events, EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// MongoDB connection URI from environment variables
const uri = process.env.MONGODB_URI;
const dbName = process.env.DB_NAME || 'sfrr';
const collectionName = 'embedResponses';

// Default embed color
const embedColor = '#626CE3';

// Initialize MongoDB connection
let cachedResponses = null;
let lastCacheTime = 0;
const CACHE_TTL = 3600000; // 1 hour in milliseconds

// Function to fetch responses from MongoDB or fallback to local JSON
async function getResponses() {
    // Return cached responses if they exist and are not expired
    const now = Date.now();
    if (cachedResponses && (now - lastCacheTime < CACHE_TTL)) {
        return cachedResponses;
    }

    // Try to get from MongoDB first
    if (uri) {
        const client = new MongoClient(uri);
        try {
            await client.connect();
            const db = client.db(dbName);
            const collection = db.collection(collectionName);
            
            // Fetch the responses document
            const document = await collection.findOne({ type: 'embed2-menu' });
            
            if (document) {
                cachedResponses = document.responses;
                lastCacheTime = now;
                await client.close();
                return cachedResponses;
            }
            
            // If no document exists, create default responses and store them
            const defaultResponses = createDefaultResponses();
            try {
                await collection.insertOne({
                    type: 'embed2-menu',
                    responses: defaultResponses
                });
                console.log('✅ Created default embed2-menu responses in MongoDB');
            } catch (insertError) {
                console.error('❌ Error saving default responses to MongoDB:', insertError);
            }
            
            cachedResponses = defaultResponses;
            lastCacheTime = now;
            await client.close();
            return cachedResponses;
            
        } catch (error) {
            console.error('❌ Error connecting to MongoDB:', error);
            await client.close().catch(() => {});
            // Fall back to local storage
        }
    }
    
    // Fallback to local JSON file
    console.log('⚠️ Using local JSON storage for embed2-menu responses');
    return loadLocalResponses();
}

// Function to load responses from local JSON file
function loadLocalResponses() {
    const localFilePath = path.join(__dirname, '..', 'data', 'embed2menu.json');
    
    try {
        // Check if file exists
        if (fs.existsSync(localFilePath)) {
            const data = fs.readFileSync(localFilePath, 'utf8');
            return JSON.parse(data);
        }
    } catch (error) {
        console.error('❌ Error reading local embed2menu.json:', error);
    }
    
    // If file doesn't exist or can't be read, create default responses
    const defaultResponses = createDefaultResponses();
    
    // Save default responses to file
    try {
        // Ensure directory exists
        const dir = path.join(__dirname, '..', 'data');
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        fs.writeFileSync(localFilePath, JSON.stringify(defaultResponses, null, 2));
        console.log('✅ Created default embed2menu.json file');
    } catch (error) {
        console.error('❌ Error saving default responses to file:', error);
    }
    
    return defaultResponses;
}

// Function to create default responses
function createDefaultResponses() {
    const roleplayEmbeds = [
        new EmbedBuilder()
            .setColor(embedColor)
            .setTitle('Server Rules')
            .setDescription(' > 1. You must be 13 years of age or older; this condition is not only a requirement of GVRC but also of the Discord Terms of Service.\n 2. Respect must always be shown, regardless of a person\'s race, gender, religion, political views, sexual orientation, or any other characteristic that separates them. \n 3. No NSFW (not safe for work) content or spamming is permitted in any of the chats. This includes in-game as well as in Discord. \n 4. In any of our voice or text channels, you aren\'t allowed to make loud noises, post extremely loud videos or audios, or do anything else that affects others. \n 5. Any order given by a staff member must be obeyed at all times. \n 6. Advertising is not permitted in GVRC channels. Advertising in DMs is permitted if it isn\'t connected to GVRS as well as you haven\'t sent it to a numerous amount of people.\n 7. You cannot use alternate accounts to rejoin the group if you have been banned. The only way to get your banned appeal is by appealing it with the link attached to your Dyno message. Before doing that it must say your ban is appealable.\n 8. Neither drama from outside groups nor drama based on events or punishments should be introduced into the GVRC. No comments that are criticizing other roleplay communities or groups are permitted.8. You\'re not allowed to ask for re-invites or a session to be hosted. This also includes even hinting at a session to be hosted.\n 9. Do not alter evidence, such as but not limited to past messages that have been deleted, text that was not stated that has been edited using inspect elements or similar tools, or messages or photographs that have been photoshopped. \n 10. DMs are moderated if it is related to GVRC. This includes, but is not restricted to; offensive language, advertising, harassment, discrimination, and NSFW material.\n 11.  This is an English-only server. Do not use other languages to bypass the chat filter.')
            .setFooter({
                text: 'Greenville Roleplay Corporation',
                iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
            })
            .toJSON(),

            new EmbedBuilder()
            .setColor(embedColor)
            .setTitle('Roleplay Information')
            .setDescription(' > 1. Combat Logging is not allowed and will result in a ban. \n 2. Fail-Roleplay Speeds are the following: Strict Peacetime: 65, Normal Peacetime: 75, Peacetime Off 90 Peacetime Off Priority User: 100. \n3. Exchanging is highly enforced, which means you may NOT hit and run users. Exchanging is to be done on the side of the 4. road, at a safe spot. Hit and running will result in an infraction. \n 4. Running from Law Enforcement is only allowed when you have been granted priority while Peacetime has been turned off, doing so at any other time will result in an infraction. \n 5. Traffic lights are not to be run by anyone, other than a user with priority while peacetime has been disabled. \n 6. Scenes are not to be interfered with, this includes staff scenes, and emergency services scenes, doing so will result in an infraction. \n7. Vehicles are not to be driven that you don\'t have the proper roles for, this includes Slotted Vehicle Exempt (SVE), Banned Vehicle Exempt (BVE), and Blacklisted Vehicle Exempt (BLVE). \n 8. Horn abuse is not allowed and will result in an infraction. \n 9. Cop baiting is not allowed, and will result in an infraction. \n 10. You may receive 8 monthly tickets, which will turn into an infraction. Tickets are NOT to be paid off, this is only for roleplay purposes.')
            .setFooter({
                text: 'Greenville Roleplay Corporation',
                iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
            })
            .toJSON(),

    ];


    return {
        'roleplay-info': { embeds: roleplayEmbeds },
        'roleplay-info2': { embeds: roleplayEmbeds }
    };
}

// Function to convert stored JSON to Discord.js objects
function prepareResponse(responseData, option) {
    const response = { ...responseData[option] };
    
    // Convert embeds from JSON to EmbedBuilder objects
    if (response.embeds) {
        response.embeds = response.embeds.map(embedData => {
            return EmbedBuilder.from(embedData);
        });
    }
    
    // Convert components from JSON to ActionRowBuilder objects
    if (response.components) {
        response.components = response.components.map(rowData => {
            return ActionRowBuilder.from(rowData);
        });
    }
    
    return response;
}

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isStringSelectMenu() || interaction.customId !== 'embed2-menu') return;

        try {
            // Get the selected option
            const selectedOption = interaction.values[0];
            
            // Fetch responses from MongoDB or local storage
            const responses = await getResponses();
            
            // Check if the selected option exists
            if (!responses[selectedOption]) {
                return await interaction.reply({
                    content: 'Invalid selection. Please try again.',
                    ephemeral: true
                });
            }
            
            // Prepare the response data
            const responseData = prepareResponse(responses, selectedOption);
            
            // Log the interaction for analytics
            console.log(`User ${interaction.user.tag} selected option: ${selectedOption}`);
            
            // Reply with the appropriate response
            await interaction.reply({
                ...responseData,
                ephemeral: true
            });
            
        } catch (error) {
            console.error('Error in embed2menuevent:', error);
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        content: 'An error occurred while processing your selection.',
                        ephemeral: true
                    });
                } catch (err) {
                    console.error('Error sending error message:', err);
                }
            }
        }
    },
};
