const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('release')
    .setDescription('Release a session with details and a join link.')
    .addStringOption(option =>
      option.setName('peacetime')
        .setDescription('Peacetime status')
        .setRequired(true)
        .addChoices(
          { name: 'On', value: 'On' },
          { name: 'Off', value: 'Off' }
        ))
    .addStringOption(option =>
      option.setName('frp-speeds')
        .setDescription('FRP speeds status')
        .setRequired(true)
        .addChoices(
          { name: '75 MPH', value: '75' },
          { name: '65 MPH', value: '65' },
        ))
    .addStringOption(option =>
      option.setName('double-warnings')
        .setDescription('Double warnings status')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('link')
        .setDescription('Session link')
        .setRequired(true))
    .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles),

  async execute(interaction) {
    try {
      const staffRoleId = '1362775746329841796';
      const logChannelId = '1384018163330715700';
  
      if (!interaction.member.roles.cache.has(staffRoleId)) {
        await interaction.reply({ content: 'You do not have permission to use this command!', ephemeral: true });
        return;
      }

      await interaction.deferReply({ ephemeral: true });
      
      const peacetime = interaction.options.getString('peacetime');
      const frpSpeeds = interaction.options.getString('frp-speeds');
      const doubleWarnings = interaction.options.getString('double-warnings');
      const link = interaction.options.getString('link');

      const embed = new EmbedBuilder()
        .setTitle('Session Release')
        .setDescription(`${interaction.user} is hosting a session. Before joining, ensure you have read both, the below server information and ⁠⁠the information.

__Session Information__
**> FRP Speeds: ${frpSpeeds}
> Double Warnings: ${doubleWarnings}
> Peacetime Status: ${peacetime}**

          Please DM ${interaction.user} if you are unable to join this session.`)
        .setColor('#626CE3')
        .setThumbnail('https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&')
        .setFooter({
          text: 'Greenville Roleplay Corporation',
          iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
        })
        .setTimestamp();

      const buttons = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId(`session_link_${link}`)
            .setLabel('Session Link')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.followUp({ content: 'Session released!', ephemeral: true });

      await interaction.channel.send({
        content: `@here`,
        embeds: [embed],
        components: [buttons],
        allowedMentions: { 
          parse: ['everyone'],
          roles: ['1335364652497895424']
        }
      });

    } catch (error) {
      console.error('Error in release command:', error);
      await interaction.followUp({ 
        content: 'There was an error while executing this command!', 
        ephemeral: true 
      }).catch(console.error);
    }
  }
};
