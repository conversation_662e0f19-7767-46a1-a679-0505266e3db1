const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed7')
    .setDescription('Displays suggestion submission format.'),

  async execute(interaction) {
    const embedColor = '#626CE3';
    const staffRoleId = '1314078903522037802';

    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    await interaction.reply({ content: 'Embed7 Message Released!', ephemeral: true });

    const infoEmbed = new EmbedBuilder()
      .setColor(embedColor)
      .setTitle('Suggestion')
      .setDescription(
        '- This is where you may request anything apart from the ones with their own channels. This means you may request things like payments or even give suggestions.\n\n' +
        '**Rules:**\n' +
        '1. Follow the correct format\n' +
        '2. Do not side-chat\n' +
        '3. If you’d like to give thoughts or opinions on a suggestion, open a thread named “Discussion” or something related\n' +
        '4. Be respectful and avoid leaking private information\n\n' +
        'Failure to abide by these rules will result in punishment.\n\n' +
        '**Format:**\n' +
        '```markdown\n' +
        '@Ping yourself\n' +
        'Date:\n' +
        'Your Suggestion / Request\n' +
        '```'
      )
      .setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
      });

    await interaction.channel.send({ 
      embeds: [infoEmbed]
    });

    const logChannelId = '1334707231555452952';
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
      const logEmbed = new EmbedBuilder()
        .setTitle('Command Executed')
        .setDescription('The `/embed7` command was executed.')
        .addFields(
          { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
          { name: 'User ID', value: `${interaction.user.id}`, inline: true },
          { name: 'Channel', value: `${interaction.channel.name}`, inline: true }
        )
        .setColor(embedColor)
        .setTimestamp();

      await logChannel.send({ embeds: [logEmbed] });
    }
  },
};
