const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed1')
    .setDescription('Displays server information and rules'),

  async execute(interaction) {
    const embedColor = '#626CE3';
    const staffRoleId = '1314078903522037802';

    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    await interaction.reply({ content: 'Embed1 Message Released!', ephemeral: true });

    const infoEmbed = new EmbedBuilder()
      .setColor(embedColor)
      .setTitle('Applications')
      .setDescription(
        ' > This channel serves as a dedicated space for showcasing various applications and opportunities available for you to explore and apply for. Whether you\'re seeking career advancements, academic programs, grants, or other opportunities, this channel provides valuable insights and resources to guide you through the application process. Stay updated with the latest openings and ensure you don\'t miss out on potential opportunities that align with your goals and aspirations.\n\n'
      )
      .setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
      });

    const menu = new ActionRowBuilder().addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('embed1-menu')
        .setPlaceholder('Click here for more information')
        .addOptions([
          {
            label: 'Applications',
            description: 'Click this option to view our applications',
            value: 'roleplay-info',
          }
        ])
    );

    await interaction.channel.send({ 
      embeds: [infoEmbed], 
      components: [menu] 
    });

    const logChannelId = '1334707231555452952';
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
      const logEmbed = new EmbedBuilder()
        .setTitle('Command Executed')
        .setDescription('The `/embed1` command was executed.')
        .addFields(
          { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
          { name: 'User ID', value: `${interaction.user.id}`, inline: true },
          { name: 'Channel', value: `${interaction.channel.name}`, inline: true }
        )
        .setColor(embedColor)
        .setTimestamp();

      await logChannel.send({ embeds: [logEmbed] });
    }
  },
};
