const { <PERSON>lash<PERSON>ommandBuilder, PermissionsBitField, MessageFlags } = require('discord.js');
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>tonStyle, ActionRowBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('unclaim')
        .setDescription('Unclaim a ticket'),
    
    async execute(interaction) {
        const supportRoleId = '1363682766855606272';
        const seniorStaffRoleId = '1363386638490927296'; // Add senior staff role ID if needed

        // Check if user has the support role
        if (!interaction.member.roles.cache.has(supportRoleId)) {
            await interaction.reply({ 
                content: 'You do not have permission to unclaim tickets.', 
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Check if this is a ticket channel
        if (!interaction.channel.name.startsWith('staff-support-') && 
            !interaction.channel.name.startsWith('member-report-') && 
            !interaction.channel.name.startsWith('partnership-')) {
            await interaction.reply({ 
                content: 'This is not a ticket!', 
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Fetch messages and convert to array for easier manipulation
        const messages = Array.from((await interaction.channel.messages.fetch({ limit: 100 })).values());
        
        // Sort messages by timestamp, newest first
        messages.sort((a, b) => b.createdTimestamp - a.createdTimestamp);

        // Find the most recent claim or unclaim message
        const lastClaimMessage = messages.find(msg => 
            msg.content && msg.content.includes('has claimed this ticket')
        );
        
        const lastUnclaimMessage = messages.find(msg => 
            msg.content && msg.content.includes('has unclaimed this ticket')
        );

        // If there's no claim message or the last unclaim is more recent than the last claim
        if (!lastClaimMessage || (lastUnclaimMessage && lastUnclaimMessage.createdTimestamp > lastClaimMessage.createdTimestamp)) {
            await interaction.reply({ 
                content: 'This ticket has not been claimed yet.', 
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Check if the user trying to unclaim is the one who claimed it
        const claimedUserId = lastClaimMessage.content.split('<@')[1]?.split('>')[0];
        if (!claimedUserId || claimedUserId !== interaction.user.id) {
            await interaction.reply({ 
                content: 'You can only unclaim tickets that you have claimed.', 
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Get the first message to find the ticket creator
        const firstMessage = messages[messages.length - 1]; // Get the oldest message
        let ticketCreator = null;
        
        // Try to get the ticket creator from the channel object first
        if (interaction.channel.ticketCreatorId) {
            ticketCreator = interaction.channel.ticketCreatorId;
        } 
        // Then try to extract from the first message embed
        else if (firstMessage?.embeds[0]?.description) {
            ticketCreator = firstMessage.embeds[0].description.match(/<@(\d+)>/)?.[1];
        }

        if (!ticketCreator) {
            await interaction.reply({ 
                content: 'Could not determine ticket creator. Please contact an administrator.', 
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Update channel permissions
        await interaction.channel.permissionOverwrites.set([
            // Default permissions (everyone)
            {
                id: interaction.guild.id,
                deny: [PermissionsBitField.Flags.ViewChannel],
            },
            // Support role permissions
            {
                id: supportRoleId,
                allow: [PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ViewChannel],
            },
            // Senior staff permissions (if needed)
            {
                id: seniorStaffRoleId,
                allow: [PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ViewChannel],
            },
            // Ticket creator permissions
            {
                id: ticketCreator,
                allow: [PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ViewChannel],
            }
        ]);

        // Delete the claim message
        try {
            await lastClaimMessage.delete();
        } catch (error) {
            console.error('Error deleting claim message:', error);
            // Continue even if we can't delete the message
        }

        // Send unclaim message
        await interaction.channel.send({
            content: `<@${interaction.user.id}> has unclaimed this ticket.`
        });

        // Find the original ticket message with buttons
        const ticketMessage = messages.find(msg => 
            msg.embeds?.length > 0 && 
            msg.components?.length > 0 && 
            msg.components[0].components.some(c => c.customId === 'claimTicket' || c.customId === 'closeTicket')
        );

        // If we found the original message, update it to re-enable the claim button
        if (ticketMessage) {
            try {
                const originalEmbed = ticketMessage.embeds[0];
                // Remove the "Ticket claimed by" line if present
                let description = originalEmbed.description;
                if (description.includes('\n\n**Ticket claimed by:**')) {
                    description = description.split('\n\n**Ticket claimed by:**')[0];
                }
                
                const updatedEmbed = EmbedBuilder.from(originalEmbed)
                    .setDescription(description);
                
                const closeButton = new ButtonBuilder()
                    .setCustomId('closeTicket')
                    .setLabel('Close Ticket')
                    .setStyle(ButtonStyle.Danger);

                const claimButton = new ButtonBuilder()
                    .setCustomId('claimTicket')
                    .setLabel('Claim Ticket')
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(false);

                const row = new ActionRowBuilder()
                    .addComponents(closeButton, claimButton);

                await ticketMessage.edit({
                    embeds: [updatedEmbed],
                    components: [row]
                });
            } catch (error) {
                console.error('Error updating ticket message:', error);
                // Continue even if we can't update the message
            }
        }

        await interaction.reply({ 
            content: 'You have successfully unclaimed this ticket.', 
            flags: MessageFlags.Ephemeral
        });
    },
};

