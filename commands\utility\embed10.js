const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed10')
    .setDescription('Displays staff in training rules.'),

  async execute(interaction) {
    const embedColor = '#626CE3';
    const staffRoleId = '1314078903522037802';

    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    await interaction.reply({ content: 'Embed10 Message Released!', ephemeral: true });

    const rulesEmbed = new EmbedBuilder()
      .setColor(embedColor)
      .setTitle('SECTION I: Rules and Regulations')
      .setDescription(
`1) Stay professional and respectful at all times.  
2) You may not harass any member of GVRC.  
3) Do not cause drama or break rules in other roleplay servers.  
4) Don’t instigate situations. De-escalate them professionally.  
5) Do not leak staff-only information or documents.  
6) Abuse of power will result in termination and blacklist.  
7) Follow the Chain of Command (CoC) for questions.  
8) Use proper grammar—avoid abbreviations at scenes.  
9) Do not take action against staff of equal or higher rank.  
10) Do not create channels, roles, or rank users without permission.  
11) Do not edit or delete your own warnings/modlogs.  
12) Get Discord IDs by checking messages, not by pinging with "Ignore."  
13) Keep session pings short and simple (e.g., "Server?").  
14) Use the leakers channel before every session and block listed users.  
15) Rule violations may lead to serious staff punishment.  
16) You must be in Hosting VC while hosting/co-hosting.  
17) After infractions, use: \`-rolepersist (user) (Infraction 1/2)\` with Dyno.  
18) To co-host/attend a session, say "Attendee" in early-access.`)

      .setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
      });

    const rules2Embed = new EmbedBuilder()
      .setColor(embedColor)
      .setTitle('SECTION II: Commands & Staff Vehicle Colors')
      .setDescription(
`__Startup Commands:__  
- Startup: \`/startup\`  
- Early Access: \`/early\`
- Release: \`/release\`
- Setting Up: \`/settingup\`
- Co-host: \`/cohost\`  
- Re-invites: \`/re-invites\`
- Over: \`/over\`

__Staff Vehicle Colors:__  
You may use any undercover vehicle. Make sure it uses the proper paint code below:  
**Black:** \`8, 8, 8\` (5% shine)`)
.setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
      });

    const rules3Embed = new EmbedBuilder()
      .setColor(embedColor)
      .setTitle('SECTION III: Staff Formats')
      .setDescription(
`**Session Information:**  
> @here, (@user) is hosting a session!  
> Peacetime: **Strict/Normal**  
> FRP Speed: **70/80 MPH**  
> Kick = Infraction  
> Double Infractions: Enabled/Disabled  
> Read all #information before joining. Rules are strictly enforced.  
> Link: \`[Insert your session link here]\`

\`\`\`diff
- PRE-RELEASE REGULATIONS:
\`\`\`
The following are the maximum allowed in pre-release:  
- 6 Law Enforcement  
- 2 Greenville Fire Department  
- 4 Membership/Nitro Members  

__Session Regenerate Tool:__  
https://sesh.fyi/timestamp/`)

.setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
      });

    // Send all embeds
    await interaction.channel.send({ 
      embeds: [rulesEmbed, rules2Embed, rules3Embed] 
    });

    // Logging
    const logChannelId = '1334707231555452952';
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
      const logEmbed = new EmbedBuilder()
        .setTitle('Command Executed')
        .setDescription('The `/embed9` command was executed.')
        .addFields(
          { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
          { name: 'User ID', value: `${interaction.user.id}`, inline: true },
          { name: 'Channel', value: `${interaction.channel.name}`, inline: true }
        )
        .setColor(embedColor)
        .setTimestamp();

      await logChannel.send({ embeds: [logEmbed] });
    }
  },
};
