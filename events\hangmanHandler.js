const { Events, EmbedBuilder } = require('discord.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isButton() || !interaction.customId.startsWith('hangman_')) return;

        const [_, gameId, letter] = interaction.customId.split('_');
        const gameData = global.hangmanGames.get(gameId);

        if (!gameData || gameData.gameOver) return;

        const { word, guessed, mistakes } = gameData;
        
        if (!guessed.includes(letter)) {
            gameData.guessed.push(letter);
            if (!word.includes(letter)) {
                gameData.mistakes++;
            }
        }

        const hangmanStates = [
            '```\n  +---+\n      |\n      |\n      |\n      |\n=========```',
            '```\n  +---+\n  O   |\n      |\n      |\n      |\n=========```',
            '```\n  +---+\n  O   |\n  |   |\n      |\n      |\n=========```',
            '```\n  +---+\n  O   |\n /|   |\n      |\n      |\n=========```',
            '```\n  +---+\n  O   |\n /|\\  |\n      |\n      |\n=========```',
            '```\n  +---+\n  O   |\n /|\\  |\n /    |\n      |\n=========```',
            '```\n  +---+\n  O   |\n /|\\  |\n / \\  |\n      |\n=========```'
        ];

        const currentWord = word.split('').map(char => gameData.guessed.includes(char) ? char : '_').join(' ');
        const won = !currentWord.includes('_');
        const lost = gameData.mistakes >= 6;

        if (won || lost) {
            gameData.gameOver = true;
        }

        const embed = new EmbedBuilder()
            .setTitle('Hangman Game')
            .setDescription(
                `${hangmanStates[gameData.mistakes]}\n` +
                `Word: ${currentWord}\n` +
                `Guessed Letters: ${gameData.guessed.join(', ')}\n` +
                (won ? '🎉 You won!' : lost ? `💀 Game Over! The word was: ${word}` : '')
            )
            .setColor(won ? '#00FF00' : lost ? '#FF0000' : '#2B2D31')
            .setFooter({ text: `Player: ${interaction.user.tag}` });

        await interaction.update({ embeds: [embed], components: interaction.message.components });
    },
};