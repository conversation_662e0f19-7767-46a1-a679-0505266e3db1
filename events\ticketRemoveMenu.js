const { Events } = require('discord.js');
const { removeTicket, removeAllTickets } = require('../commands/utility/ticketremove.js');
const { userDataCache } = require('./profilebutton.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isStringSelectMenu()) return;
        if (!interaction.customId.startsWith('remove_ticket_')) return;

        // Defer the update immediately to prevent interaction expiration
        await interaction.deferUpdate();

        const userId = interaction.customId.split('_')[2];
        const selectedValue = interaction.values[0];

        let success;
        let message;

        if (selectedValue === 'all') {
            success = await removeAllTickets(userId, interaction);
            message = success ? 'All tickets have been removed.' : 'Failed to remove all tickets. Please try again.';
        } else {
            const selectedIndex = parseInt(selectedValue);
            success = await removeTicket(userId, selectedIndex, interaction);
            message = success ? `Ticket ${selectedIndex + 1} has been removed.` : 'Failed to remove the ticket. Please try again.';
        }

        // Add a small delay (3 seconds)
        await new Promise(resolve => setTimeout(resolve, 3000));

        if (success) {
            // Safely clear the cache
            if (userDataCache && userDataCache.tickets) {
                userDataCache.tickets.delete(userId);
            }
            
            await interaction.editReply({ 
                content: message, 
                embeds: [], 
                components: [] 
            });
        } else {
            await interaction.editReply({ 
                content: message, 
                embeds: [], 
                components: [] 
            });
        }
    },
};

