const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('settingup')
    .setDescription('Notifies that staff, boosters, emergency services, and content creators can join.'),

  async execute(interaction) {
    const staffRoleId = '1362775746329841796'; // Staff role ID
    const logChannelId = '1384018163330715700'; // Log channel ID

    // Check if the user has the staff role
    if (!interaction.member.roles.cache.has(staffRoleId)) {
      await interaction.reply({ content: 'You do not have permission to use this command.', ephemeral: true });
      return;
    }

    // Create the embed message
    const embed = new EmbedBuilder()
      .setTitle('Setting Up!')
      .setDescription(`> <@${interaction.user.id}> is now Setting Up, Early Access, P/S & Staff are now joining.`)
      .setColor('#6c78fc')
      .setThumbnail('https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&')
      .setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
      });

    // Reply to the user privately
    await interaction.reply({ content: 'Setting message released!', ephemeral: true });

    // Send the embed publicly in the channel
    await interaction.channel.send({ embeds: [embed] });

    // Log the command execution
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel && logChannel.isTextBased()) {
      const logMessage = `The \`/settingup\` command was executed by ${interaction.user.tag} in <#${interaction.channel.id}>.`;
      await logChannel.send({ content: logMessage });
    }
  }
};
