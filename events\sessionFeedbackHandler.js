const { Events, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isButton() || interaction.customId !== 'session_feedback') return;

        try {
            // Create the modal
            const modal = new ModalBuilder()
                .setCustomId('session_feedback_modal')
                .setTitle('Session Feedback');

            // Add the host input at the top
            const hostInput = new TextInputBuilder()
                .setCustomId('host')
                .setLabel('Who was the host?')
                .setStyle(TextInputStyle.Short)
                .setMinLength(1)
                .setMaxLength(100)
                .setPlaceholder('Enter the host\'s name')
                .setRequired(true);

            // Add the rating input
            const ratingInput = new TextInputBuilder()
                .setCustomId('rating')
                .setLabel('Session Rating (1-10)')
                .setStyle(TextInputStyle.Short)
                .setMinLength(1)
                .setMaxLength(2)
                .setPlaceholder('Enter a number between 1 and 10')
                .setRequired(true);

            // Add the improvement input
            const improvementInput = new TextInputBuilder()
                .setCustomId('improvement')
                .setLabel('What can we improve?')
                .setStyle(TextInputStyle.Paragraph)
                .setMinLength(1)
                .setMaxLength(1000)
                .setPlaceholder('Make the server have less FRP and must have 2+ characters')
                .setRequired(true);

            // Add the notes input
            const notesInput = new TextInputBuilder()
                .setCustomId('notes')
                .setLabel('Additional Notes (Optional)')
                .setStyle(TextInputStyle.Paragraph)
                .setMinLength(1)
                .setMaxLength(1000)
                .setRequired(false);

            // Create action rows for each input
            const hostRow = new ActionRowBuilder().addComponents(hostInput);
            const firstRow = new ActionRowBuilder().addComponents(ratingInput);
            const secondRow = new ActionRowBuilder().addComponents(improvementInput);
            const thirdRow = new ActionRowBuilder().addComponents(notesInput);

            // Add inputs to the modal
            modal.addComponents(hostRow, firstRow, secondRow, thirdRow);

            // Show the modal
            await interaction.showModal(modal);

        } catch (error) {
            console.error('Error handling session feedback button:', error);
            // Only try to respond if we haven't already
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        content: 'There was an error processing your feedback. Please try again.',
                        ephemeral: true
                    });
                } catch (err) {
                    console.error('Error sending error response:', err);
                }
            }
        }
    },
};

