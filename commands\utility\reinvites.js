const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');

const DATA_PATH = path.join(__dirname, '../../data/reinvites.json');

// Function to save the reinvites link
function saveReinvitesLink(link) {
    try {
        const dir = path.dirname(DATA_PATH);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        fs.writeFileSync(DATA_PATH, JSON.stringify({ link }, null, 2));
        global.reinviteLink = link;
    } catch (error) {
        console.error('Error saving reinvites link:', error);
    }
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName('re-invites')
    .setDescription('Re-invite session link')
    .addStringOption(option => 
      option.setName('link')
        .setDescription('Re-invites link (must be a valid URL)')
        .setRequired(true)),

  async execute(interaction) {
    const staffRoleId = '1362775746329841796'; // Replace with your actual staff role ID
    const logChannelId = '1384018163330715700'; // Replace with your actual log channel ID

    if (!interaction.member.roles.cache.has(staffRoleId)) {
      await interaction.reply({ content: 'You do not have permission to use this command!', ephemeral: true });
      return;
    }

    const link = interaction.options.getString('link');
    
    // Save the link to file
    saveReinvitesLink(link);

    // Validate URL format
    if (!link.startsWith('https://')) {
      return interaction.reply({ content: 'Please provide a valid URL that starts with `https://`.', ephemeral: true });
    }

    const embed = new EmbedBuilder()
      .setTitle('Session Reinvites')
      .setDescription(`> Welcome back! You’ve been reinvited to join the **Greenville Roleplay Corporation** session. Please use the following link to join:  

> We look forward to seeing you in the game! Enjoy the session and follow the rules for a smooth experience.`)
      .setColor('#626CE3')
      .setThumbnail('https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&')
      .setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
      });

    const row = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('reinvites_link')
          .setLabel('Re-invites Link')
          .setStyle(ButtonStyle.Primary)
      );

    await interaction.reply({ content: 'Re-invites Released!', ephemeral: true });

    await interaction.channel.send({
      content: '@here',
      embeds: [embed],
      components: [row],
      allowedMentions: { parse: ['everyone'] }
    });

    // Log the command execution
    const logEmbed = new EmbedBuilder()
      .setTitle('Command Execution Log')
      .setDescription(`**Command:** /re-invites\n**Executed By:** ${interaction.user.tag} (${interaction.user.id})\n**Link:** ${link}`)
      .setColor('6c78fc')
      .setTimestamp();

    const logChannel = await interaction.client.channels.fetch(logChannelId);
    if (logChannel) {
      await logChannel.send({ embeds: [logEmbed] });
    }
  }
};
