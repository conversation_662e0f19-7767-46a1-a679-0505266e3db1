const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const path = require('path');
const fs = require('fs');

const sessionLogsPath = path.join(__dirname, '../../data/sessionLogs');

// Role configurations
const ROLE_REQUIREMENTS = {
    '1314078918571069450': 3.0, // 3 points required
    '1314078908865577031': 3.0, // 3 points required
    '1360598598114345020': 2.0, // 2 points required
};

const EXEMPT_ROLES = [
    '1314078905967050832',
    '1374162168408309931',
    '1314078903522037802'
];

function calculatePoints(userId) {
    const filePath = path.join(sessionLogsPath, `${userId}.json`);
    try {
        if (!fs.existsSync(filePath)) {
            return 0;
        }

        const data = fs.readFileSync(filePath, 'utf8');
        const sessions = JSON.parse(data);

        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

        const recentSessions = sessions.filter(session => {
            const sessionDate = new Date(session.timestamp);
            return sessionDate >= oneWeekAgo;
        });

        return recentSessions.reduce((total, session) => {
            return total + (session.hostType === 'Host' ? 1 : 0.5);
        }, 0);
    } catch (error) {
        console.error(`Error calculating points for ${userId}:`, error);
        return 0;
    }
}

function isExempt(member) {
    return EXEMPT_ROLES.some(roleId => member.roles.cache.has(roleId));
}

function resetSessionLogs(userId) {
    const filePath = path.join(sessionLogsPath, `${userId}.json`);
    try {
        if (fs.existsSync(filePath)) {
            // Delete the file to reset sessions
            fs.unlinkSync(filePath);
        }
    } catch (error) {
        console.error(`Error resetting session logs for ${userId}:`, error);
    }
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('quotacheck')
        .setDescription('Check who passed and failed the weekly quota'),

    async execute(interaction) {
        try {
            // Only allow staff to use this command
            if (!interaction.member.roles.cache.has('1314078905967050832')) {
                return await interaction.reply({
                    content: 'You do not have permission to use this command!',
                    ephemeral: true
                });
            }

            await interaction.deferReply({ ephemeral: true });
            
            await interaction.guild.members.fetch({ force: true });
            
            const guild = interaction.guild;
            let passedMembers = [];
            let failedMembers = [];
            let exemptMembers = [];

            const membersToCheck = new Set();
            for (const roleId of Object.keys(ROLE_REQUIREMENTS)) {
                const role = guild.roles.cache.get(roleId);
                if (role) {
                    role.members.forEach(member => membersToCheck.add(member));
                }
            }

            // Process all members
            for (const member of membersToCheck) {
                if (isExempt(member)) {
                    exemptMembers.push({
                        id: member.id,
                        mention: `<@${member.id}>`
                    });
                    continue;
                }

                const points = calculatePoints(member.id);
                let required = 0;
                
                for (const [roleId, requirement] of Object.entries(ROLE_REQUIREMENTS)) {
                    if (member.roles.cache.has(roleId) && requirement > required) {
                        required = requirement;
                    }
                }

                const memberInfo = {
                    id: member.id,
                    mention: `<@${member.id}>`,
                    points: points.toFixed(1),
                    required: required
                };

                if (points >= required) {
                    passedMembers.push(memberInfo);
                    // Reset session logs for members who passed
                    resetSessionLogs(member.id);
                } else {
                    failedMembers.push(memberInfo);
                }
            }

            // Check exempt roles
            for (const exemptRoleId of EXEMPT_ROLES) {
                const role = guild.roles.cache.get(exemptRoleId);
                if (role) {
                    role.members.forEach(member => {
                        if (!exemptMembers.some(m => m.id === member.id)) {
                            exemptMembers.push({
                                id: member.id,
                                mention: `<@${member.id}>`
                            });
                        }
                    });
                }
            }

            const passedEmbed = new EmbedBuilder()
                .setTitle('Passed Quota')
                .setDescription(passedMembers.length > 0 
                    ? passedMembers.map(m => `${m.mention} (${m.points}/${m.required} points)`).join('\n')
                    : 'No members passed the quota')
                .setColor('#626CE3');

            const failedEmbed = new EmbedBuilder()
                .setTitle('Failed Quota')
                .setDescription(failedMembers.length > 0 
                    ? failedMembers.map(m => `${m.mention} (${m.points}/${m.required} points)`).join('\n')
                    : 'No members failed the quota')
                .setColor('#626CE3');

            const exemptEmbed = new EmbedBuilder()
                .setTitle('Exempt from Quota')
                .setDescription(exemptMembers.length > 0 
                    ? exemptMembers.map(m => m.mention).join('\n')
                    : 'No exempt members found')
                .setColor('#626CE3');

            // Send results to channel
            await interaction.channel.send({ embeds: [passedEmbed, failedEmbed, exemptEmbed] });
            
            // Send confirmation to command executor
            await interaction.editReply({ 
                content: `Quota check completed! Session logs have been reset for ${passedMembers.length} members who passed.`,
                ephemeral: true 
            });

        } catch (error) {
            console.error('Error in quotacheckv2 command:', error);
            await interaction.editReply({ 
                content: 'An error occurred while running the quota check.',
                ephemeral: true 
            });
        }
    }
};