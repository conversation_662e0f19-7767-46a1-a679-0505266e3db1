const { SlashCommandBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('civilianpass')
        .setDescription('Approve a civilian application.')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('User to give the civilian role to')
                .setRequired(true)),

    async execute(interaction) {
        const allowedRole = '1385051083684184114'; // Role that can use this command
        const roleToAdd = '1322045053014900787';   // Civilian role
        const roleToRemove = '1385050048429166665'; // Pending role
        const logChannelId = '1371687466523820156'; // Channel to log approvals

        // Check if the user has the required role
        if (!interaction.member.roles.cache.has(allowedRole)) {
            return interaction.reply({
                content: 'You do not have permission to use this command.',
                ephemeral: true
            });
        }

        const member = interaction.options.getMember('user');
        const reviewer = interaction.user;

        try {
            // Add and remove roles
            await member.roles.add(roleToAdd);
            await member.roles.remove(roleToRemove);

            // Log the action
            const logChannel = interaction.guild.channels.cache.get(logChannelId);
            if (logChannel) {
                await logChannel.send({
                    content: `<@${member.id}> **Civilian Application Passed** Welcome to Greenville Roleplay Corporation. The information contained <#1314082319711211521>, <#1314079179741991023> & https://discord.com/channels/1310346706285625434/1358852282875121855 should be read before attending a session. Application Read By: <@${reviewer.id}>`
                });
            }

            // Reply to the approver
            await interaction.reply({
                content: `Successfully approved **${member.user.tag}**'s civilian application.`,
                ephemeral: true
            });
        } catch (error) {
            console.error(error);
            await interaction.reply({
                content: 'An error occurred while processing the request.',
                ephemeral: true
            });
        }
    }
};
