const { Events, EmbedBuilder } = require('discord.js');

// Pre-define embeds for faster access and cache them
const embedColor = '#35506e';
const embeds = {
    'session-commands': new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Session Commands')
        .setDescription(
            '**Commands:**\n' +
            '> **/startup (reactions)**: Notify others you are hosting a session.\n' +
            '> **/settingup**: Start the session after enough reactions.\n' +
            '> **/co-host**: Co-host a session.\n' +
            '> **/early-access (link)**: Allow early access for certain roles.\n' +
            '> **/release (link)**: Open the session for civilians.\n' +
            '> **/reinvites (link)**: Send reinvites post-release.\n' +
            '> **/over**: End your session officially.'
        )
        .setFooter({ text: 'Southwest Florida Roleplay Extreme' })
        .toJSON(),

    'staff-quota': new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Staff Quota Requirements')
        .setDescription(
            '**Quota Requirements:**\n' +
            '- **Staff in training**: Pass training and co-host 2 sessions.\n' +
            '- **Server Staff**: 3 sessions per week.\n' +
            '- **Senior Staff**: 2 sessions per week.\n' +
            '- **Staff Supervisor**: 1 session per week, trains SIT.\n' +
            '- **Management and higher**: Exempt from quotas, ensure smooth operations.'
        )
        .setFooter({ text: 'Southwest Florida Roleplay Extreme' })
        .toJSON()
};

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isStringSelectMenu() || interaction.customId !== 'embed3-menu') return;

        try {
            const selectedEmbed = embeds[interaction.values[0]];
            
            if (!interaction.replied) {
                try {
                    await interaction.reply({
                        embeds: selectedEmbed ? [new EmbedBuilder(selectedEmbed)] : undefined,
                        content: selectedEmbed ? undefined : 'Invalid selection. Please try again.',
                        ephemeral: true
                    });
                } catch (replyError) {
                    if (replyError.code === 10062) {
                        // Interaction expired, try to use followUp
                        try {
                            await interaction.followUp({
                                embeds: selectedEmbed ? [new EmbedBuilder(selectedEmbed)] : undefined,
                                content: selectedEmbed ? undefined : 'Invalid selection. Please try again.',
                                ephemeral: true
                            });
                        } catch (followUpError) {
                            // If both reply and followUp fail, silently handle the error
                            console.error('Failed to respond to interaction:', followUpError);
                        }
                    }
                }
            } else {
                // If already replied, use followUp
                await interaction.followUp({
                    embeds: selectedEmbed ? [new EmbedBuilder(selectedEmbed)] : undefined,
                    content: selectedEmbed ? undefined : 'Invalid selection. Please try again.',
                    ephemeral: true
                });
            }
        } catch (error) {
            // Silently handle any other errors
            console.error('Error handling interaction:', error);
        }
    },
};
