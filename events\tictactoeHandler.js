const { Events, Embed<PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        // Ensure tictactoeGames exists
        if (!global.tictactoeGames) {
            global.tictactoeGames = new Map();
        }

        if (!interaction.isButton() || !interaction.customId.startsWith('ttt_')) return;

        const [_, gameId, position] = interaction.customId.split('_');
        const gameData = global.tictactoeGames.get(gameId);

        if (!gameData) {
            return interaction.reply({ 
                content: 'This game no longer exists or has expired.', 
                ephemeral: true 
            });
        }

        if (gameData.gameOver) {
            return interaction.reply({ 
                content: 'This game has already ended.', 
                ephemeral: true 
            });
        }

        if (interaction.user.id !== gameData.currentTurn) {
            return interaction.reply({ 
                content: "It's not your turn!", 
                ephemeral: true 
            });
        }

        const index = parseInt(position);
        if (gameData.board[index] !== null) {
            return interaction.reply({ 
                content: 'This position is already taken!', 
                ephemeral: true 
            });
        }

        const currentSymbol = gameData.players[0] === gameData.currentTurn ? 'X' : 'O';
        gameData.board[index] = currentSymbol;

        const winPatterns = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
            [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
            [0, 4, 8], [2, 4, 6] // Diagonals
        ];

        const hasWon = winPatterns.some(pattern => 
            pattern.every(pos => gameData.board[pos] === currentSymbol)
        );

        const isDraw = gameData.board.every(cell => cell !== null);

        if (hasWon || isDraw) {
            gameData.gameOver = true;
        } else {
            gameData.currentTurn = gameData.currentTurn === gameData.players[0] ? gameData.players[1] : gameData.players[0];
        }

        // Update the game board
        const rows = [];
        for (let i = 0; i < 3; i++) {
            const actionRow = new ActionRowBuilder();
            const buttons = [];
            
            for (let j = 0; j < 3; j++) {
                const buttonIndex = i * 3 + j;
                const button = new ButtonBuilder()
                    .setCustomId(`ttt_${gameId}_${buttonIndex}`)
                    .setLabel(gameData.board[buttonIndex] || '\u200b')
                    .setStyle(
                        gameData.board[buttonIndex] === 'X' ? ButtonStyle.Primary :
                        gameData.board[buttonIndex] === 'O' ? ButtonStyle.Danger :
                        ButtonStyle.Secondary
                    )
                    .setDisabled(gameData.gameOver);
                    
                buttons.push(button);
            }
            
            actionRow.addComponents(buttons);
            rows.push(actionRow);
        }

        const embed = new EmbedBuilder()
            .setTitle('Tic Tac Toe')
            .setDescription(
                hasWon ? `${interaction.user} wins!` :
                isDraw ? "It's a draw!" :
                `${interaction.user} (${currentSymbol}) vs ${interaction.user.id === gameData.players[0] ? 
                    await interaction.client.users.fetch(gameData.players[1]) : 
                    await interaction.client.users.fetch(gameData.players[0])} (${currentSymbol === 'X' ? 'O' : 'X'})\n` +
                `Current turn: ${gameData.currentTurn === gameData.players[0] ? 
                    await interaction.client.users.fetch(gameData.players[0]) : 
                    await interaction.client.users.fetch(gameData.players[1])}`
            )
            .setColor('#2B2D31');

        await interaction.update({
            embeds: [embed],
            components: rows
        });

        // Clean up finished games after 5 minutes
        if (gameData.gameOver) {
            setTimeout(() => {
                global.tictactoeGames.delete(gameId);
            }, 5 * 60 * 1000);
        }
    },
};
