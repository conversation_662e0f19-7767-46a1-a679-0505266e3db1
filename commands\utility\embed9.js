const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed9')
    .setDescription('Displays staff in training rules.'),

  async execute(interaction) {
    const embedColor = '#626CE3';
    const staffRoleId = '1314078903522037802';

    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    await interaction.reply({ content: 'Embed9 Message Released!', ephemeral: true });

    const rulesEmbed = new EmbedBuilder()
      .setColor(embedColor)
      .setTitle('Staff In Training Rules')
      .setDescription(
        '1. **No Cheating**\n' +
        'All training assessments, tasks, and evaluations must be completed honestly and independently.\n\n' +
        '2. **Be Honest**\n' +
        'Always provide truthful information during your training.\n\n' +
        '3. **Ask for Help When Needed**\n' +
        'If you\'re struggling to understand something, ask questions.\n\n' +
        '4. **Respect the Training Process**\n' +
        'Complete all required modules and tasks as instructed.\n\n' +
        '5. **Maintain Professionalism**\n' +
        'Conduct yourself respectfully and professionally.\n\n' +
        '6. **Follow Instructions**\n' +
        'Listen carefully to your trainers and follow all instructions.\n\n' +
        '7. **Do Not Share Answers or Materials**\n' +
        'Training materials are for personal use only.\n\n' +
        '8. **Own Your Progress**\n' +
        'Take responsibility for your learning.\n\n' +
        '9. **Confidentiality Matters**\n' +
        'Do not share any internal or sensitive information.\n\n' +
        '10. **Uphold Integrity**\n' +
        'Stay committed to honesty and ethical behavior.'
      )
      .setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
      });
      
    await interaction.channel.send({ 
      embeds: [rulesEmbed]
    });

    const logChannelId = '1334707231555452952';
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
      const logEmbed = new EmbedBuilder()
        .setTitle('Command Executed')
        .setDescription('The `/embed9` command was executed.')
        .addFields(
          { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
          { name: 'User ID', value: `${interaction.user.id}`, inline: true },
          { name: 'Channel', value: `${interaction.channel.name}`, inline: true }
        )
        .setColor(embedColor)
        .setTimestamp();

      await logChannel.send({ embeds: [logEmbed] });
    }
  },
};
