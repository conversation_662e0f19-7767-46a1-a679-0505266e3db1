const { Events, EmbedBuilder } = require('discord.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isModalSubmit() || interaction.customId !== 'session_feedback_modal') return;

        try {
            const host = interaction.fields.getTextInputValue('host');
            const rating = interaction.fields.getTextInputValue('rating');
            const improvement = interaction.fields.getTextInputValue('improvement');
            const notes = interaction.fields.getTextInputValue('notes') || 'No additional notes provided';

            // Validate rating
            const numRating = parseInt(rating);
            if (isNaN(numRating) || numRating < 1 || numRating > 10) {
                await interaction.reply({
                    content: 'Please provide a valid rating between 1 and 10.',
                    ephemeral: true
                });
                return;
            }

            // Create feedback embed
            const feedbackEmbed = new EmbedBuilder()
                .setTitle('Session Feedback Received')
                .setDescription(`Feedback from ${interaction.user}`)
                .addFields(
                    { name: 'Host', value: host, inline: true },
                    { name: 'Rating', value: `${rating}/10`, inline: true },
                    { name: 'Improvements Suggested', value: improvement },
                    { name: 'Additional Notes', value: notes }
                )
                .setColor('#6c78fc')
                .setTimestamp();

            // Send feedback to channel
            const feedbackChannelId = '1384018163330715700';
            const feedbackChannel = await interaction.client.channels.fetch(feedbackChannelId);
            
            if (feedbackChannel) {
                await feedbackChannel.send({ embeds: [feedbackEmbed] });
                await interaction.reply({
                    content: 'Thank you for your feedback!',
                    ephemeral: true
                });
            }

        } catch (error) {
            console.error('Error handling feedback modal submission:', error);
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        content: 'There was an error submitting your feedback. Please try again.',
                        ephemeral: true
                    });
                } catch (err) {
                    console.error('Error sending error response:', err);
                }
            }
        }
    },
};

