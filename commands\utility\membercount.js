const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('membercount')
        .setDescription('Displays the number of members in the server'),
    
    async execute(interaction) {
        const guild = interaction.guild;
        const memberCount = guild.memberCount;

        const embed = new EmbedBuilder()
            .setTitle('Member Count')
            .setDescription(`${memberCount} members`)
            .setColor('#626CE3')
            .setFooter({
                text: 'Greenville Roleplay Corporation',
                iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
            });
        
        await interaction.reply({
            embeds: [embed],
            ephemeral: false 
        });
    }
};
