const { Events, EmbedBuilder } = require('discord.js');

module.exports = {
  name: Events.PresenceUpdate,
  async execute(oldPresence, newPresence) {
    const guild = newPresence.guild;
    if (!guild) return;

    const user = newPresence.member;
    if (!user || user.user.bot) return;

    const status = newPresence.activities.find(a => a.type === 4); // Custom status

    const roleId = '1375244077741899837'; // Server Contributor role ID
    const logChannelId = '1314079268011249694'; // Log channel ID
    const targetText = 'discord.gg/greenvillecorporation'; // Text to check in custom status

    // Check if the role exists in this guild
    if (!guild.roles.cache.has(roleId)) {
      // Role not found, silently return without logging
      return;
    }

    // Check if bot has Manage Roles permission
    if (!guild.members.me.permissions.has('ManageRoles')) {
      // Permission missing, silently return
      return;
    }

    // Check bot's role hierarchy relative to target role
    const botHighestRolePos = guild.members.me.roles.highest.position;
    const targetRolePos = guild.roles.cache.get(roleId).position;
    if (botHighestRolePos <= targetRolePos) {
      // Bot role not high enough, silently return
      return;
    }

    const hasInvite = status?.state?.toLowerCase().includes(targetText);
    const hasRole = user.roles.cache.has(roleId);

    const logChannel = guild.channels.cache.get(logChannelId);

    try {
      if (hasInvite && !hasRole) {
        await user.roles.add(roleId);
        if (logChannel?.isTextBased()) {
          const embed = new EmbedBuilder()
            .setColor('#626CE3')
            .setTitle('Server Contributor Role Awarded')
            .setDescription(`**Thank you for the support, ${user}!**\n\nWe truly appreciate your contribution to the community. As a token of our gratitude, we’ve awarded you with the **Server Contributor** role!`)
            .setTimestamp()
            .setFooter({
              text: 'Greenville Roleplay Corporation',
              iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
            });
          await logChannel.send({ embeds: [embed] });
        }
      } else if (!hasInvite && hasRole) {
        await user.roles.remove(roleId);
      }
    } catch (err) {
      // Silently ignore errors in role add/remove to avoid spam logs
    }
  }
};
