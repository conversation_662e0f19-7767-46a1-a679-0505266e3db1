const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('purge')
        .setDescription('Delete a specified number of messages in the channel')
        .addIntegerOption(option =>
            option.setName('number')
                .setDescription('The number of messages to delete')
                .setRequired(true)),
    async execute(interaction) {
        try {
            // Defer the reply immediately
            await interaction.deferReply({ ephemeral: true });

            // Check if the user has the staff role
            const staffRoleId = '1314078903522037802';
            if (!interaction.member.roles.cache.has(staffRoleId)) {
                return await interaction.editReply('You do not have permission to use this command.');
            }

            const number = interaction.options.getInteger('number');

            // Ensure the number is between 1 and 100
            if (number < 1 || number > 100) {
                return await interaction.editReply('You need to input a number between 1 and 100.');
            }

            // Check if bot has necessary permissions
            if (!interaction.channel.permissionsFor(interaction.client.user).has(PermissionFlagsBits.ManageMessages)) {
                return await interaction.editReply('I do not have permission to delete messages in this channel.');
            }

            // Bulk delete the specified number of messages
            const deleted = await interaction.channel.bulkDelete(number, true)
                .catch(error => {
                    console.error('Error deleting messages:', error);
                    return null;
                });

            if (!deleted) {
                return await interaction.editReply('Failed to delete messages. Messages older than 14 days cannot be bulk deleted.');
            }

            // Log channel ID
            const logChannelId = '1381460621744406629';
            const logChannel = interaction.client.channels.cache.get(logChannelId);

            // Create log embed
            const logEmbed = new EmbedBuilder()
                .setTitle('Purge Command Used')
                .setDescription(`Messages were purged in ${interaction.channel}`)
                .addFields(
                    { name: 'Moderator', value: `${interaction.user.tag} (${interaction.user.id})`, inline: true },
                    { name: 'Channel', value: `${interaction.channel.name} (${interaction.channel.id})`, inline: true },
                    { name: 'Amount', value: `${number} messages`, inline: true }
                )
                .setColor('#02a0dd')
                .setTimestamp()
                .setFooter({
                    text: 'Southwest Florida Roleplay Hub',
                    iconURL: 'https://cdn.discordapp.com/attachments/1370926891048898650/1381064331399266406/Screenshot_2025-05-16_212900.png?ex=68477963&is=684627e3&hm=d4a22622b62d6b3d17912c834da0bd4c1321963555510012f1bdbfd863997878&'
                });

            // Update log embed with results
            logEmbed.addFields({ name: 'Result', value: `Successfully deleted ${deleted.size} messages`, inline: false });
            
            // Send log
            if (logChannel) {
                await logChannel.send({ embeds: [logEmbed] });
            }

            return await interaction.editReply(`Successfully deleted ${deleted.size} messages.`);

        } catch (error) {
            console.error('Error in purge command:', error);
            
            // Try to respond with an error message
            try {
                if (interaction.deferred) {
                    await interaction.editReply('There was an error executing this command.');
                } else {
                    await interaction.reply({ content: 'There was an error executing this command.', ephemeral: true });
                }
            } catch (e) {
                console.error('Error sending error message:', e);
            }
        }
    }
};
