const { Events, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, ButtonStyle } = require('discord.js');
const path = require('path');
const fs = require('fs/promises'); // Using promises version for better async handling
const sessionLogsPath = path.join(__dirname, '../data/sessionLogs');
const SESSIONS_PER_PAGE = 4;

// Cache for session logs to improve performance
const sessionLogsCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

async function loadSessionLogs(userId) {
    try {
        // Check cache first
        const cachedData = sessionLogsCache.get(userId);
        if (cachedData && Date.now() - cachedData.timestamp < CACHE_TTL) {
            return cachedData.data;
        }

        const filePath = path.join(sessionLogsPath, `${userId}.json`);
        const exists = await fs.access(filePath).then(() => true).catch(() => false);
        
        if (!exists) {
            sessionLogsCache.set(userId, { data: [], timestamp: Date.now() });
            return [];
        }

        let data;
        try {
            const fileContent = await fs.readFile(filePath, 'utf8');
            data = JSON.parse(fileContent);
            
            // Ensure data is an array
            if (!Array.isArray(data)) {
                console.error(`User  ${userId} session logs is not an array, resetting`);
                data = [];
            }
            
            // Normalize hostType for older entries
            data = data.map(session => {
                if (session.hostType === undefined) {
                    // Try to determine hostType from other fields for backward compatibility
                    if (session.type === 'Session Host') {
                        session.hostType = 'Host';
                    } else if (session.type === 'Session Co-host') {
                        session.hostType = 'Co-host';
                    }
                }
                return session;
            });
            
        } catch (parseError) {
            console.error(`Error parsing session logs for user ${userId}:`, parseError);
            data = [];
        }
        
        sessionLogsCache.set(userId, { data, timestamp: Date.now() });
        return data;
    } catch (error) {
        console.error('Error loading session logs:', error);
        return [];
    }
}

function createSessionEmbeds(sessions, page = 0) {
    if (!sessions?.length) return [];

    const startIdx = page * SESSIONS_PER_PAGE;
    const endIdx = Math.min(startIdx + SESSIONS_PER_PAGE, sessions.length);
    const pageItems = sessions.slice(startIdx, endIdx);

    return pageItems.map(session => {
        const embed = new EmbedBuilder()
            .setTitle('Session Details')
            .setColor('#626CE3');
            
        // Handle different session data formats
        const description = [];
        
        if (session.userId) {
            description.push(`**User :** <@${session.userId}>`);
        }
        
        if (session.date) {
            description.push(`**Date:** ${session.date}`);
        }
        
        if (session.duration) {
            description.push(`**Duration:** ${session.duration}`);
        } else if (session.sessionStart && session.sessionEnd) {
            description.push(`**Session Time:** ${session.sessionStart} - ${session.sessionEnd}`);
        }
        
        if (session.coHosts) {
            description.push(`**Co-hosts:** ${session.coHosts}`);
        }
        
        if (session.hostType) {
            description.push(`**Role:** ${session.hostType}`);
        }
        
        embed.setDescription(description.join('\n'));
        
        if (session.timestamp) {
            embed.setFooter({ 
                text: `Session logged on ${new Date(session.timestamp).toLocaleString()}` 
            });
            embed.setTimestamp(new Date(session.timestamp));
        } else {
            embed.setTimestamp();
        }
        
        return embed;
    });
}

function createPaginationButtons(currentPage, totalPages, type, userId) {
    const buttons = [];
    
    // First page button
    buttons.push(
        new ButtonBuilder()
            .setCustomId(`${type}_first_${userId}_${currentPage}`)
            .setLabel('First')
            .setStyle(ButtonStyle.Primary)
            .setDisabled(currentPage <= 0)
    );
    
    // Previous page button
    buttons.push(
        new ButtonBuilder()
            .setCustomId(`${type}_prev_${userId}_${currentPage}`)
            .setLabel('Previous')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(currentPage <= 0)
    );

    // Next page button
    buttons.push(
        new ButtonBuilder()
            .setCustomId(`${type}_next_${userId}_${currentPage}`)
            .setLabel('Next')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(currentPage >= totalPages - 1)
    );

    // Last page button
    buttons.push(
        new ButtonBuilder()
            .setCustomId(`${type}_last_${userId}_${currentPage}`)
            .setLabel('Last')
            .setStyle(ButtonStyle.Primary)
            .setDisabled(currentPage >= totalPages - 1)
    );

    return new ActionRowBuilder().addComponents(buttons);
}

async function handlePaginationUpdate(interaction, type, action, userId, currentPage) {
    const page = parseInt(currentPage);
    let newPage = page;
    
    const sessionLogs = await loadSessionLogs(userId);
    const isHosts = type === 'hosts';
    
    // Strict filtering logic - exact match only
    const relevantSessions = sessionLogs.filter(log => {
        // Skip entries without hostType
        if (!log.hostType) return false;
        
        // Exact match only - no partial matching
        return isHosts ? log.hostType === 'Host' : log.hostType === 'Co-host';
    });
    
    const totalPages = Math.ceil(relevantSessions.length / SESSIONS_PER_PAGE) || 1;
    
    // Determine the new page based on the action
    switch (action) {
        case 'first':
            newPage = 0;
            break;
        case 'last':
            newPage = totalPages - 1;
            break;
        case 'prev':
            newPage = Math.max(0, page - 1);
            break;
        case 'next':
            newPage = Math.min(totalPages - 1, page + 1);
            break;
    }
    
    const embeds = createSessionEmbeds(relevantSessions, newPage);
    const buttons = createPaginationButtons(newPage, totalPages, type, userId);

    const pageIndicatorEmbed = new EmbedBuilder()
        .setTitle(`${isHosts ? 'Session Hosts' : 'Session Co-hosts'} History`)
        .setDescription(`Showing page ${newPage + 1} of ${totalPages} (${relevantSessions.length} total sessions)`)
        .setColor('#626CE3')
        .setAuthor({ 
            name: interaction.guild.members.cache.get(userId)?.displayName || 'User ', 
            iconURL: interaction.guild.members.cache.get(userId)?.displayAvatarURL() 
        });

    return {
        embeds: [pageIndicatorEmbed, ...embeds],
        components: buttons.components.length > 0 ? [buttons] : []
    };
}

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isButton()) return;

        const customId = interaction.customId;
        if (!customId.startsWith('show_session_') && 
            !customId.startsWith('hosts_') && 
            !customId.startsWith('cohosts_')) {
            return;
        }

        try {
            // Immediately defer the interaction to prevent timeouts
            if (!interaction.deferred && !interaction.replied) {
                await interaction.deferUpdate();
            }

            if (customId.includes('_prev_') || customId.includes('_next_') || 
                customId.includes('_first_') || customId.includes('_last_')) {
                const [type, action, userId, currentPage] = customId.split('_');
                const response = await handlePaginationUpdate(interaction, type, action, userId, currentPage);
                
                // Edit the same message for pagination
                await interaction.editReply(response).catch(error => {
                    console.error('Error updating pagination response:', error);
                });
                return;
            }

            if (customId.startsWith('show_session_hosts_') || 
                customId.startsWith('show_session_cohosts_')) {
                const parts = customId.split('_');
                const userId = parts[parts.length - 1];
                const isHosts = customId.startsWith('show_session_hosts_');

                const sessionLogs = await loadSessionLogs(userId);
                
                // Use strict filtering - exact match only
                const relevantSessions = sessionLogs.filter(log => {
                    if (!log.hostType) return false;
                    // Exact match only - no partial matching
                    return isHosts ? log.hostType === 'Host' : log.hostType === 'Co-host';
                });

                if (!relevantSessions.length) {
                    await interaction.followUp({
                        content: `No ${isHosts ? 'host' : 'co-host'} sessions found.`,
                        ephemeral: true
                    }).catch(console.error);
                    return;
                }

                const totalPages = Math.ceil(relevantSessions.length / SESSIONS_PER_PAGE) || 1;
                const embeds = createSessionEmbeds(relevantSessions, 0);
                const buttons = createPaginationButtons(0, totalPages, isHosts ? 'hosts' : 'cohosts', userId);

                const pageIndicatorEmbed = new EmbedBuilder()
                    .setTitle(`${isHosts ? 'Session Hosts' : 'Session Co-hosts'} History`)
                    .setDescription(`Showing page 1 of ${totalPages} (${relevantSessions.length} total sessions)`)
                    .setColor('#626CE3')
                    .setAuthor({ 
                        name: interaction.guild.members.cache.get(userId)?.displayName || 'User ', 
                        iconURL: interaction.guild.members.cache.get(userId)?.displayAvatarURL() 
                    });

                const responseData = {
                    embeds: [pageIndicatorEmbed, ...embeds],
                    components: totalPages > 1 ? [buttons] : [],
                    ephemeral: true
                };

                // Send the initial message
                await interaction.followUp(responseData).catch(console.error);
            }
        } catch (error) {
            console.error('Error handling staff profile button:', error);
            
            const errorMessage = {
                content: 'An error occurred while processing your request. Please try again.',
                ephemeral: true
            };

            try {
                await interaction.followUp(errorMessage).catch(() => {});
            } catch (followUpError) {
                console.error('Error sending error message:', followUpError);
            }
        }
    }
};
