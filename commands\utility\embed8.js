const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed8')
    .setDescription('Displays partnership information.'),

  async execute(interaction) {
    const embedColor = '#626CE3';
    const staffRoleId = '1314078903522037802';

    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    await interaction.reply({ content: 'Embed8 Message Released!', ephemeral: true });

    const infoEmbed = new EmbedBuilder()
      .setColor(embedColor)
      .setTitle('Partner')
      .setDescription(
        'If you wish to partner with GVRC, please note that your server must meet the following criteria: your server must have 2,500 members.\n\n' +
        '**Southwest Florida Roleplay Corporation**\n' +
        '[Click here to continue](https://discord.gg/gvserver)\n\n' +
        '**Greenville Roleplay Justice**\n' +
        '[Click here to continue](https://discord.gg/gvrj)\n\n' +
        '**Greenville Roleplay Community**\n' +
        '[Click here to continue](https://discord.gg/gvrpc)\n\n' +
        '**Southland FivePD**\n' +
        '[Click here to continue](https://discord.gg/pZAarbRfBw)\n\n' +
        '**Shadow\'s Greenville Roleplay**\n' +
        '[Click here to continue](https://discord.gg/sgr)'
      )
      .setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
      });

    await interaction.channel.send({ 
      embeds: [infoEmbed]
    });

    const logChannelId = '1334707231555452952';
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
      const logEmbed = new EmbedBuilder()
        .setTitle('Command Executed')
        .setDescription('The `/embed8` command was executed.')
        .addFields(
          { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
          { name: 'User ID', value: `${interaction.user.id}`, inline: true },
          { name: 'Channel', value: `${interaction.channel.name}`, inline: true }
        )
        .setColor(embedColor)
        .setTimestamp();

      await logChannel.send({ embeds: [logEmbed] });
    }
  },
};
