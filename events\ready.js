const { ActivityType } = require('discord.js');
const mongoose = require('mongoose');
const mongoURL = process.env.mongoURL;

module.exports = {
    name: 'ready',
    once: true,
    async execute(client) {
        console.log(`${client.user?.username} is online! (${client.user?.id})`);

        // Array of status objects
        const statuses = [
            {
                type: ActivityType.Watching,
                name: 'discord.gg/greenvillecorporation',
                status: 'dnd',
            },
            {
                type: ActivityType.Watching,
                name: 'discord.gg/greenvillecorporation',
                status: 'dnd',
            }
        ];

        let currentIndex = 0;

        // Function to update bot's activity and status
        const updateStatus = () => {
            const { type, name, status } = statuses[currentIndex];
            client.user.setPresence({
                activities: [{ name, type }],
                status,
            });
            
            // Move to next status (loop back to beginning if at the end)
            currentIndex = (currentIndex + 1) % statuses.length;
        };

        // Set initial activity
        updateStatus();

        // Rotate status every 10 seconds
        const rotationInterval = 10 * 1000; // 10 seconds
        setInterval(updateStatus, rotationInterval);
    },
};
