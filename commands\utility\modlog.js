const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  PermissionFlagsBits
} = require('discord.js');
const fs = require('fs');
const path = require('path');

const allowedRoleIds = ['1314078903522037802', '1374162168408309931', '1362775746329841796'];

// Helper function to get modlog data
function getModLogData() {
  const filePath = path.join(__dirname, '../../data/modlogs.json');
  
  // Create data directory if it doesn't exist
  const dataDir = path.dirname(filePath);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  // Create file if it doesn't exist
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, JSON.stringify([], null, 2));
    return [];
  }
  
  try {
    const data = fs.readF<PERSON><PERSON><PERSON>(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading modlog data:', error);
    return [];
  }
}

// Helper function to save modlog entry
function saveModLogEntry(entry) {
  try {
    const filePath = path.join(__dirname, '../../data/modlogs.json');
    const logs = getModLogData();
    
    // Generate case ID
    const caseId = logs.length > 0 ? Math.max(...logs.map(log => log.caseId)) + 1 : 1;
    entry.caseId = caseId;
    entry.timestamp = new Date().toISOString();
    
    logs.push(entry);
    fs.writeFileSync(filePath, JSON.stringify(logs, null, 2));
    
    return caseId;
  } catch (error) {
    console.error('Error saving modlog entry:', error);
    return null;
  }
}

// Helper function to format action type
function formatAction(action) {
  const actionMap = {
    'ban': 'Ban',
    'unban': 'Unban',
    'kick': 'Kick',
    'warn': 'Warning',
    'mute': 'Mute',
    'unmute': 'Unmute',
    'timeout': 'Timeout',
    'untimeout': 'Remove Timeout'
  };
  return actionMap[action] || action;
}

// Helper function to create modlog embed
function createModLogEmbed(logs, page, totalPages, targetUser = null, guildName) {
  const embed = new EmbedBuilder()
    .setColor('#626CE3')
    .setTimestamp()
    .setFooter({
      text: `Page ${page}/${totalPages} • Greenville Roleplay Corporation`,
      iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
    });

  if (targetUser) {
    embed.setTitle(`Moderation Logs for ${targetUser.tag}`)
         .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }));
  } else {
    embed.setTitle(`Server Moderation Logs`)
         .setDescription(`Showing moderation logs for **${guildName}**`);
  }

  if (logs.length === 0) {
    embed.addFields({ name: 'No Logs Found', value: 'No moderation logs found for this query.' });
    return embed;
  }

  const startIndex = (page - 1) * 5;
  const endIndex = Math.min(startIndex + 5, logs.length);
  const pageEntries = logs.slice(startIndex, endIndex);

  pageEntries.forEach(log => {
    const date = new Date(log.timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    let fieldValue = `**Moderator:** <@${log.moderatorId}>\n**Reason:** ${log.reason}`;
    if (log.proof) fieldValue += `\n**Proof:** ${log.proof}`;
    if (log.duration) fieldValue += `\n**Duration:** ${log.duration}`;
    fieldValue += `\n**Date:** ${date}`;

    embed.addFields({
      name: `Case #${log.caseId} - ${formatAction(log.action)}`,
      value: fieldValue,
      inline: false
    });
  });

  return embed;
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName('modlog')
    .setDescription('View moderation logs for a user or the server.')
    .addSubcommand(subcommand =>
      subcommand
        .setName('user')
        .setDescription('View moderation logs for a specific user.')
        .addUserOption(option =>
          option.setName('target')
            .setDescription('The user to view logs for.')
            .setRequired(true)))
    .addSubcommand(subcommand =>
      subcommand
        .setName('server')
        .setDescription('View all server moderation logs.'))
    .addSubcommand(subcommand =>
      subcommand
        .setName('case')
        .setDescription('View a specific moderation case.')
        .addIntegerOption(option =>
          option.setName('id')
            .setDescription('The case ID to view.')
            .setRequired(true)))
    .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers),

  async execute(interaction) {
    // Check permissions
    const hasPermission = allowedRoleIds.some(roleId => 
      interaction.member.roles.cache.has(roleId)
    );

    if (!hasPermission) {
      return interaction.reply({
        content: 'You do not have permission to use this command.',
        ephemeral: true
      });
    }

    await interaction.deferReply();

    const subcommand = interaction.options.getSubcommand();
    const logs = getModLogData();

    if (subcommand === 'case') {
      const caseId = interaction.options.getInteger('id');
      const logEntry = logs.find(log => log.caseId === caseId);

      if (!logEntry) {
        return interaction.editReply({
          content: `No moderation case found with ID #${caseId}.`
        });
      }

      const date = new Date(logEntry.timestamp).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });

      const embed = new EmbedBuilder()
        .setTitle(`Moderation Case #${logEntry.caseId}`)
        .setColor('#626CE3')
        .addFields(
          { name: 'Action', value: formatAction(logEntry.action), inline: true },
          { name: 'Target User', value: `<@${logEntry.targetUserId}>`, inline: true },
          { name: 'Moderator', value: `<@${logEntry.moderatorId}>`, inline: true },
          { name: 'Reason', value: logEntry.reason, inline: false }
        )
        .setTimestamp(new Date(logEntry.timestamp))
        .setFooter({
          text: 'Greenville Roleplay Corporation',
          iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
        });

      if (logEntry.proof) {
        embed.addFields({ name: 'Proof', value: logEntry.proof, inline: false });
      }

      if (logEntry.duration) {
        embed.addFields({ name: 'Duration', value: logEntry.duration, inline: true });
      }

      return interaction.editReply({ embeds: [embed] });
    }

    let filteredLogs = logs.filter(log => log.guildId === interaction.guild.id);

    if (subcommand === 'user') {
      const targetUser = interaction.options.getUser('target');
      filteredLogs = filteredLogs.filter(log => log.targetUserId === targetUser.id);
      
      if (filteredLogs.length === 0) {
        return interaction.editReply({
          content: `No moderation logs found for ${targetUser.tag}.`
        });
      }
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    const totalPages = Math.ceil(filteredLogs.length / 5) || 1;
    let currentPage = 1;

    const targetUser = subcommand === 'user' ? interaction.options.getUser('target') : null;
    const embed = createModLogEmbed(filteredLogs, currentPage, totalPages, targetUser, interaction.guild.name);

    const row = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('modlog_prev')
          .setLabel('Previous')
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(currentPage === 1),
        new ButtonBuilder()
          .setCustomId('modlog_next')
          .setLabel('Next')
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(currentPage === totalPages)
      );

    const response = await interaction.editReply({
      embeds: [embed],
      components: totalPages > 1 ? [row] : []
    });

    if (totalPages > 1) {
      const collector = response.createMessageComponentCollector({
        time: 300000 // 5 minutes
      });

      collector.on('collect', async (buttonInteraction) => {
        if (buttonInteraction.user.id !== interaction.user.id) {
          return buttonInteraction.reply({
            content: 'You cannot interact with this menu.',
            ephemeral: true
          });
        }

        if (buttonInteraction.customId === 'modlog_prev' && currentPage > 1) {
          currentPage--;
        } else if (buttonInteraction.customId === 'modlog_next' && currentPage < totalPages) {
          currentPage++;
        }

        const newEmbed = createModLogEmbed(filteredLogs, currentPage, totalPages, targetUser, interaction.guild.name);
        const newRow = new ActionRowBuilder()
          .addComponents(
            new ButtonBuilder()
              .setCustomId('modlog_prev')
              .setLabel('Previous')
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(currentPage === 1),
            new ButtonBuilder()
              .setCustomId('modlog_next')
              .setLabel('Next')
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(currentPage === totalPages)
          );

        await buttonInteraction.update({
          embeds: [newEmbed],
          components: [newRow]
        });
      });

      collector.on('end', () => {
        const disabledRow = new ActionRowBuilder()
          .addComponents(
            new ButtonBuilder()
              .setCustomId('modlog_prev')
              .setLabel('Previous')
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(true),
            new ButtonBuilder()
              .setCustomId('modlog_next')
              .setLabel('Next')
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(true)
          );

        interaction.editReply({ components: [disabledRow] }).catch(() => {});
      });
    }
  },

  // Export the helper function for other commands to use
  saveModLogEntry
};
