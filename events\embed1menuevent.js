const { Events, EmbedBuilder } = require('discord.js');

module.exports = {
  name: Events.InteractionCreate,
  async execute(interaction) {
    // Only respond to StringSelectMenu interactions with customId 'embed1-menu'
    if (!interaction.isStringSelectMenu()) return;
    if (interaction.customId !== 'embed1-menu') return;

    try {
      const selected = interaction.values[0];

      if (selected === 'roleplay-info') {
        // Defer the reply to avoid "unknown interaction" errors
        await interaction.deferReply({ ephemeral: true });

        const embedColor = '#626CE3';

        const applicationsEmbed = new EmbedBuilder()
          .setColor(embedColor)
          .setTitle('Applications')
          .setDescription(
            ' > [Wisconsin State Patrol](https://forms.gle/Ks8Pb9Ttaxdiu36X7)\n' +
            ' > [Outagamie County Sheriff\'s Office](https://forms.gle/3dj15v6wQujCnsDZ7)\n' +
            ' > [Greenville Fire Department](Coming soon)\n' +
            ' > [Wisconsin DOT](Coming Soon)'
          )
          .setFooter({
            text: 'Greenville Roleplay Corporation',
            iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
          });

        // Edit original deferred reply with the embed
        await interaction.editReply({ embeds: [applicationsEmbed] });
      } else {
        // Unknown option fallback
        await interaction.reply({ content: 'Invalid selection.', ephemeral: true });
      }
    } catch (error) {
      console.error('Error handling embed1 menu interaction:', error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({ content: 'There was an error while processing your request.', ephemeral: true });
      }
    }
  },
};
