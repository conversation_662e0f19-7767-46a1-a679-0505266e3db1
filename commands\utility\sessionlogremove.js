const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON><PERSON>, EmbedBuilder, StringSelectMenuBuilder, ActionRowBuilder } = require('discord.js');
const path = require('path');
const fs = require('fs');

const sessionLogsPath = path.join(__dirname, '../../data/sessionLogs');

function loadSessionLogs(userId) {
    const filePath = path.join(sessionLogsPath, `${userId}.json`);
    try {
        return fs.existsSync(filePath) ? JSON.parse(fs.readFileSync(filePath, 'utf8')) : [];
    } catch (error) {
        console.error('Error loading session logs:', error);
        return [];
    }
}

function removeSpecificSession(userId, sessionIndex) {
    const filePath = path.join(sessionLogsPath, `${userId}.json`);
    try {
        if (!fs.existsSync(filePath)) {
            return { success: false, message: 'No session logs found for this user.' };
        }

        const sessions = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        if (sessionIndex >= sessions.length) {
            return { success: false, message: 'Invalid session index.' };
        }

        const removedSession = sessions.splice(sessionIndex, 1)[0];
        fs.writeFileSync(filePath, JSON.stringify(sessions, null, 2));

        return { 
            success: true, 
            message: `Session removed successfully.`,
            removedSession,
            remainingCount: sessions.length
        };
    } catch (error) {
        console.error('Error removing session:', error);
        return { success: false, message: 'Error removing session.' };
    }
}

function removeAllSessionsByType(userId, type) {
    const filePath = path.join(sessionLogsPath, `${userId}.json`);
    try {
        if (!fs.existsSync(filePath)) {
            return { success: false, message: 'No session logs found for this user.' };
        }

        if (type === 'all') {
            fs.unlinkSync(filePath);
            return { success: true, message: 'All session logs removed successfully.' };
        }

        const sessions = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        const filteredSessions = sessions.filter(session => session.hostType !== type);
        fs.writeFileSync(filePath, JSON.stringify(filteredSessions, null, 2));

        return { 
            success: true, 
            message: `All ${type} sessions removed successfully.`,
            remainingCount: filteredSessions.length
        };
    } catch (error) {
        console.error('Error removing sessions:', error);
        return { success: false, message: 'Error removing session logs.' };
    }
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('sessionlogremove')
        .setDescription('Remove session logs for a user')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Select the user to remove session logs for')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('removesessionlog')
                .setDescription('Select which type of session logs to remove')
                .setRequired(true)
                .addChoices(
                    { name: 'Host Sessions', value: 'Host' },
                    { name: 'Co-host Sessions', value: 'Co-host' },
                    { name: 'Remove All Session Logs', value: 'all' }
                )),

    async execute(interaction) {
        try {
            const staffRoleId = '1314078905967050832';
            if (!interaction.member.roles.cache.has(staffRoleId)) {
                await interaction.reply({
                    content: 'You do not have permission to use this command!',
                    ephemeral: true
                });
                return;
            }

            const targetUser = interaction.options.getUser('user');
            const removeType = interaction.options.getString('removesessionlog');

            // Load current sessions
            const sessions = loadSessionLogs(targetUser.id);

            if (removeType === 'all') {
                const result = removeAllSessionsByType(targetUser.id, 'all');
                await handleRemovalResponse(interaction, result, targetUser, removeType);
                return;
            }

            // Filter sessions by type
            const typeSessions = sessions.filter(session => session.hostType === removeType);

            if (typeSessions.length === 0) {
                await interaction.reply({
                    content: `No ${removeType} sessions found for this user.`,
                    ephemeral: true
                });
                return;
            }

            // Create select menu for individual sessions
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`remove_session_${targetUser.id}_${removeType}`)
                .setPlaceholder(`Select ${removeType} session to remove`)
                .addOptions(
                    typeSessions.map((session, index) => ({
                        label: `Session on ${session.date}`,
                        description: `Duration: ${session.duration}`,
                        value: index.toString()
                    }))
                )
                .addOptions({
                    label: `Remove All ${removeType} Sessions`,
                    description: `Removes all ${removeType} sessions for this user`,
                    value: 'all'
                });

            const row = new ActionRowBuilder().addComponents(selectMenu);

            if (!interaction.replied) {
                await interaction.reply({
                    content: `Please select which ${removeType} session to remove:`,
                    components: [row],
                    ephemeral: true
                });
            }

        } catch (error) {
            console.error('Error in sessionlogremove command:', error);
            if (!interaction.replied) {
                await interaction.reply({
                    content: 'There was an error while executing this command!',
                    ephemeral: true
                });
            }
        }
    },
    removeSpecificSession,
    removeAllSessionsByType,
    handleRemovalResponse
};

async function handleRemovalResponse(interaction, result, targetUser, removeType) {
    if (!interaction.isRepliable()) return; // Check if interaction can still be replied to

    if (!result.success) {
        try {
            if (interaction.replied) {
                await interaction.followUp({
                    content: result.message,
                    ephemeral: true
                });
            } else {
                await interaction.reply({
                    content: result.message,
                    ephemeral: true
                });
            }
        } catch (error) {
            console.error('Error sending removal response:', error);
        }
        return;
    }

    const beforeSessions = loadSessionLogs(targetUser.id);
    const beforeCounts = {
        hosts: beforeSessions.filter(log => log.hostType === 'Host').length,
        cohosts: beforeSessions.filter(log => log.hostType === 'Co-host').length
    };

    const embed = new EmbedBuilder()
        .setTitle('Session Logs Removed')
        .setDescription(`Session logs removed for ${targetUser}`)
        .addFields(
            { name: 'Removal Type', value: removeType, inline: true },
            { name: 'Before Removal', value: `Hosts: ${beforeCounts.hosts} | Co-hosts: ${beforeCounts.cohosts}`, inline: true }
        )
        .setColor('#02a0dd')
        .setTimestamp()
        .setFooter({
            text: 'Southwest Florida Roleplay Hub',
            iconURL: 'https://cdn.discordapp.com/attachments/1370926891048898650/1381064331399266406/Screenshot_2025-05-16_212900.png?ex=68477963&is=684627e3&hm=d4a22622b62d6b3d17912c834da0bd4c1321963555510012f1bdbfd863997878&'
        });

    if (removeType !== 'all' && result.remainingCount !== undefined) {
        const afterSessions = loadSessionLogs(targetUser.id);
        const afterCounts = {
            hosts: afterSessions.filter(log => log.hostType === 'Host').length,
            cohosts: afterSessions.filter(log => log.hostType === 'Co-host').length
        };
        embed.addFields({
            name: 'After Removal',
            value: `Hosts: ${afterCounts.hosts} | Co-hosts: ${afterCounts.cohosts}`,
            inline: true
        });
    }

    try {
        if (interaction.replied) {
            await interaction.followUp({
                content: result.message,
                embeds: [embed],
                ephemeral: true
            });
        } else {
            await interaction.reply({
                content: result.message,
                embeds: [embed],
                ephemeral: true
            });
        }

        const logChannelId = '1381460621744406629';
        const logChannel = interaction.client.channels.cache.get(logChannelId);
        if (logChannel) {
            await logChannel.send({ embeds: [embed] }).catch(console.error);
        }
    } catch (error) {
        console.error('Error sending removal response:', error);
    }
}


