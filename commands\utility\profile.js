const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, But<PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonStyle } = require('discord.js');
const axios = require('axios');
const path = require('path');
const fs = require('fs');

const dataFolderPath = path.join(__dirname, '../../data/vehicleData');
const ticketsDirPath = path.join(__dirname, '../../data/tickets');
const licensesDirPath = path.join(__dirname, '../../data/licenses');

const loadVehicleCount = (discordUserId) => {
    try {
        const vehicleFilePath = path.join(__dirname, '..', '..', 'data', 'vehicleData', `${discordUserId}.json`);
        if (fs.existsSync(vehicleFilePath)) {
            const data = JSON.parse(fs.readFileSync(vehicleFilePath, 'utf8'));
            if (data.items) return data.items.length;
            if (Array.isArray(data)) return data.length;
            return 0;
        }
        return 0;
    } catch (error) {
        console.error('Error loading vehicle count:', error);
        return 0;
    }
};

module.exports = {
    data: new SlashCommandBuilder()
        .setName('profile')
        .setDescription('Displays your Roblox profile or another user\'s profile.')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Select a user to view their Roblox profile. If not selected, shows your profile.')),

    async execute(interaction) {
        try {

            const selectedUser = interaction.options.getUser('user') || interaction.user;

            if (selectedUser.bot) {
                return await interaction.reply({
                    content: 'You cannot view profiles of discord bots!',
                    ephemeral: true
                });
            }

            const discordUserId = selectedUser.id;
            const guildId = interaction.guild.id;

            const vehicleData = loadJsonFile(path.join(dataFolderPath, `${discordUserId}.json`)) || [];
            const ticketsData = loadJsonFile(path.join(ticketsDirPath, `${discordUserId}.json`)) || [];

            await interaction.deferReply({ ephemeral: false });

            let robloxProfilePicture = selectedUser.displayAvatarURL({ format: 'png', size: 512 });
            let licenseStatus = 'Valid';
            const licenseFilePath = path.join(licensesDirPath, `${discordUserId}.json`);
            if (fs.existsSync(licenseFilePath)) {
                const licenses = JSON.parse(fs.readFileSync(licenseFilePath, 'utf8'));
                if (licenses.length > 0) {
                    const latestLicense = licenses[licenses.length - 1];
                    licenseStatus = latestLicense.status;
                }
            }

            const vehicleCount = loadVehicleCount(discordUserId);

            try {
                const bloxlinkApiKey = process.env.BLOXLINK;
                const bloxlinkApiUrl = `https://api.blox.link/v4/public/guilds/${guildId}/discord-to-roblox/${discordUserId}`;

                const bloxlinkResponse = await axios.get(bloxlinkApiUrl, {
                    headers: {
                        'Authorization': bloxlinkApiKey,
                        'api-key': bloxlinkApiKey
                    }
                });

                if (!bloxlinkResponse.data?.robloxID) throw new Error('No Roblox ID');

                const robloxUserId = bloxlinkResponse.data.robloxID;
                const robloxUserResponse = await axios.get(`https://users.roblox.com/v1/users/${robloxUserId}`);
                const robloxUsername = robloxUserResponse.data.name;

                const thumbnailResponse = await axios.get(
                    `https://thumbnails.roblox.com/v1/users/avatar-headshot?userIds=${robloxUserId}&size=420x420&format=Png&isCircular=false`
                );
                robloxProfilePicture = thumbnailResponse.data.data[0]?.imageUrl || robloxProfilePicture;

                const profileEmbed = new EmbedBuilder()
                    .setTitle(`${selectedUser.username}'s Profile`)
                    .setDescription(`
Welcome to <@${discordUserId}>'s profile. Here, you can find your register vehicles, tickets, and more information.

** __Profile Information__**

**Discord User:** <@${discordUserId}>
**Roblox Username:** [${robloxUsername}](https://www.roblox.com/users/${robloxUserId}/profile)
**License Status:** ${licenseStatus}
**Vehicle Count:** ${vehicleCount}
**Ticket Count:** ${ticketsData.length}
                    `)
                    .setColor('#626CE3')
                    .setThumbnail(robloxProfilePicture)
                    .setFooter({
                        text: 'Greenville Roleplay Corporation',
                        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
                    })
                    .setTimestamp();

                const actionRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`show_registrations_${discordUserId}`)
                            .setLabel('View Vehicles')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_tickets_${discordUserId}`)
                            .setLabel('View Tickets')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_balance_${discordUserId}`)
                            .setLabel('View Balance')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_modlog_${discordUserId}_${interaction.user.id}`)
                            .setLabel('View Modlog')
                            .setStyle(ButtonStyle.Secondary)
                    );

                return await interaction.editReply({
                    embeds: [profileEmbed],
                    components: [actionRow]
                });

            } catch (error) {
                console.warn('Bloxlink API failed, falling back to Discord profile.');

                const fallbackEmbed = new EmbedBuilder()
                    .setTitle(`${selectedUser.username}'s Profile`)
                    .setDescription(`
Welcome to <@${discordUserId}>'s profile. Here, you can find your register vehicles, tickets, and more information.

** __Profile Information__**

**Discord User:** <@${discordUserId}>
**License Status:** ${licenseStatus}
**Vehicle Count:** ${vehicleCount}
**Ticket Count:** ${ticketsData.length}
                    `)
                    .setColor('#626CE3')
                    .setThumbnail(robloxProfilePicture)
                    .setFooter({
                        text: 'Greenville Roleplay Corporation',
                        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
                    })
                    .setTimestamp();

                const actionRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`show_registrations_${discordUserId}`)
                            .setLabel('View Vehicles')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_tickets_${discordUserId}`)
                            .setLabel('View Tickets')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_balance_${discordUserId}`)
                            .setLabel('View Balance')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_modlog_${discordUserId}_${interaction.user.id}`)
                            .setLabel('View Modlogs')
                            .setStyle(ButtonStyle.Secondary)
                    );

                return await interaction.editReply({
                    embeds: [fallbackEmbed],
                    components: [actionRow]
                });
            }

        } catch (error) {
            console.error('Error:', error);
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: 'An error occurred while fetching the profile information.',
                    ephemeral: true
                });
            } else {
                await interaction.editReply({
                    content: 'An error occurred while fetching the profile information.'
                });
            }
        }
    }
};

function loadJsonFile(filePath) {
    try {
        return fs.existsSync(filePath) ? JSON.parse(fs.readFileSync(filePath, 'utf8')) : [];
    } catch {
        return [];
    }
}
