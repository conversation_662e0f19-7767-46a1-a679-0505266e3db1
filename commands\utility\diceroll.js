// Step 1: Import the tools we need
const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

// Step 2: Export our command (this makes it available to the bot)
module.exports = {
    // Step 3: Define the command structure
    data: new SlashCommandBuilder()
        .setName('diceroll')
        .setDescription('Roll a dice!')
        .addIntegerOption(option =>
            option.setName('sides')
                .setDescription('Number of sides on the dice (default: 6)')
                .setRequired(false)
                .setMinValue(2)
                .setMaxValue(100)
        ),

    // Step 4: The function that runs when someone uses the command
    async execute(interaction) {
        // Step 5: Get the user's input (or use default)
        const sides = interaction.options.getInteger('sides') || 6;
        
        // Step 6: Generate a random number
        const result = Math.floor(Math.random() * sides) + 1;
        
        // Step 7: Create a nice-looking response
        const embed = new EmbedBuilder()
            .setTitle('🎲 Dice Roll!')
            .setDescription(`You rolled a **${result}** on a ${sides}-sided dice!`)
            .setColor('#626CE3')
            .setFooter({ 
                text: `Rolled by ${interaction.user.username}`,
                iconURL: interaction.user.displayAvatarURL()
            })
            .setTimestamp();
        
        // Step 8: Send the response
        await interaction.reply({ embeds: [embed] });
    }
};
