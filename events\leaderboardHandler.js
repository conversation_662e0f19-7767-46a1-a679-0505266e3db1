const { Events } = require('discord.js');
const { createPaginationRow, createLeaderboardEmbed } = require('../commands/economy/leaderboard.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isButton()) return;
        if (!interaction.customId.startsWith('leaderboard_')) return;

        try {
            await interaction.deferUpdate();  // Defer the update immediately

            const [, action, userId, currentPage] = interaction.customId.split('_');
            
            if (interaction.user.id !== userId) {
                return await interaction.followUp({
                    content: 'Only the user who ran this command can navigate through pages.',
                    ephemeral: true
                });
            }

            if (!global.leaderboardData) {
                global.leaderboardData = new Map();
            }

            const leaderboardData = global.leaderboardData.get(interaction.message.id);
            if (!leaderboardData) {
                return await interaction.editReply({
                    content: 'This leaderboard has expired. Please run the command again.',
                    embeds: [],
                    components: []
                });
            }

            if (Date.now() - leaderboardData.timestamp > 30 * 60 * 1000) {
                global.leaderboardData.delete(interaction.message.id);
                return await interaction.editReply({
                    content: 'This leaderboard has expired. Please run the command again.',
                    embeds: [],
                    components: []
                });
            }

            const page = parseInt(currentPage);
            const newPage = action === 'next' ? page + 1 : page - 1;
            const totalPages = Math.ceil(leaderboardData.users.length / 10);

            const embed = await createLeaderboardEmbed(
                leaderboardData.users,
                newPage,
                interaction,
                totalPages
            );

            await interaction.editReply({
                embeds: [embed],
                components: [createPaginationRow(newPage, totalPages, userId)]
            });

        } catch (error) {
            console.error('Error in leaderboard button handler:', error);
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        content: 'There was an error while updating the leaderboard.',
                        ephemeral: true
                    });
                } catch (e) {
                    console.error('Failed to send error message:', e);
                }
            }
        }
    }
};