const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed3')
    .setDescription('Displays server information and rules'),

  async execute(interaction) {
    const embedColor = '#626CE3';
    const staffRoleId = '1314078903522037802';

    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    await interaction.reply({ content: 'Embed1 Message Released!', ephemeral: true });

    const infoEmbed = new EmbedBuilder()
      .setColor(embedColor)
      .setTitle('Server Startup')
      .setDescription(
        ' > Welcome to the ⁠startup channel. In this channel, you will receive a notification (ping) whenever a session startup has been initiated. These pings are important, as they signal the beginning of an upcoming event or operation. \n\n >  We kindly ask that all members take the time to thoroughly read and understand the provided civilian-related information prior to joining any session. This ensures that everyone is properly informed and prepared, contributing to a smooth and coordinated experience for all participants.'
      )
      .setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
      });


    await interaction.channel.send({ 
      embeds: [infoEmbed]
    });

    const logChannelId = '1334707231555452952';
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
      const logEmbed = new EmbedBuilder()
        .setTitle('Command Executed')
        .setDescription('The `/embed1` command was executed.')
        .addFields(
          { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
          { name: 'User ID', value: `${interaction.user.id}`, inline: true },
          { name: 'Channel', value: `${interaction.channel.name}`, inline: true }
        )
        .setColor(embedColor)
        .setTimestamp();

      await logChannel.send({ embeds: [logEmbed] });
    }
  },
};
