const { Events } = require('discord.js');
const fs = require('fs');
const path = require('path');
const { MongoClient } = require('mongodb');
const startupFile = path.join(__dirname, '../data/startup.json');

const STAFF_ROLE_ID = '1322045053014900787';
const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.DB_NAME || 'sfrr';

// Cache for startup data to avoid repeated file/DB reads
let startupDataCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 30000; // 30 seconds

module.exports = {
  name: Events.InteractionCreate,
  async execute(interaction) {
    if (!interaction.isButton()) return;
    if (!interaction.customId.startsWith('session_link_')) return;

    // Check if interaction is still valid before doing anything
    if (!interaction.isRepliable()) return;

    let hasDeferred = false;

    try {
      // Defer immediately to prevent timeout
      await interaction.deferReply({ ephemeral: true });
      hasDeferred = true;

      // Quick permission check first
      if (!interaction.member?.roles?.cache?.has(STAFF_ROLE_ID)) {
        if (interaction.deferred && !interaction.replied) {
          await interaction.editReply({
            content: 'You do not have permission to view the session link!'
          });
        }
        return;
      }

      try {
        let startupData;

        // Check cache first for faster response
        const now = Date.now();
        if (startupDataCache && (now - cacheTimestamp) < CACHE_DURATION) {
          startupData = startupDataCache;
        } else {
          // Try MongoDB first
          if (MONGODB_URI) {
            const client = new MongoClient(MONGODB_URI);
            try {
              await client.connect();
              const db = client.db(DB_NAME);
              const collection = db.collection('startup');

              startupData = await collection.findOne({ type: 'startup' });
              await client.close();
            } catch (mongoError) {
              console.error('MongoDB Error:', mongoError);
              // Fallback to JSON if MongoDB fails
              if (fs.existsSync(startupFile)) {
                startupData = JSON.parse(fs.readFileSync(startupFile, 'utf-8'));
              }
            }
          } else {
            // Use JSON if no MongoDB URI is configured
            if (fs.existsSync(startupFile)) {
              startupData = JSON.parse(fs.readFileSync(startupFile, 'utf-8'));
            }
          }

          // Cache the data
          if (startupData) {
            startupDataCache = startupData;
            cacheTimestamp = now;
          }
        }

        if (!startupData || !startupData.messageId) {
          if (interaction.deferred && !interaction.replied) {
            await interaction.editReply({
              content: 'Please wait for the host to post the startup message.'
            });
          }
          return;
        }

        const startupMessage = await interaction.channel.messages.fetch(startupData.messageId).catch(() => null);
        if (!startupMessage) {
          if (interaction.deferred && !interaction.replied) {
            await interaction.editReply({
              content: 'Startup message not found. Please wait for the host to post a new one.'
            });
          }
          return;
        }

        const reaction = startupMessage.reactions.cache.get('✅');
        if (!reaction) {
          if (interaction.deferred && !interaction.replied) {
            await interaction.editReply({
              content: `Please react to the startup message to have access to the session link. React [here](${startupMessage.url}).`
            });
          }
          return;
        }

        const userReacted = await reaction.users.fetch().then(users => users.has(interaction.user.id)).catch(() => false);
        if (!userReacted) {
          if (interaction.deferred && !interaction.replied) {
            await interaction.editReply({
              content: `Please react to the startup message to have access to the session link. React [here](${startupMessage.url}).`
            });
          }
          return;
        }

        const link = interaction.customId.replace('session_link_', '');
        if (interaction.deferred && !interaction.replied) {
          await interaction.editReply({
            content: `**Session Link:** ${link}`
          });
        }

      } catch (error) {
        console.error('Error checking startup reaction:', error);
        if (interaction.deferred && !interaction.replied) {
          await interaction.editReply({
            content: 'Please wait for the host to post the startup message.'
          });
        }
      }

    } catch (error) {
      // Only log errors that aren't Discord API errors we're trying to prevent
      if (!error.code || ![10062, 10064, 10008, 40062].includes(error.code)) {
        console.error('Error in session link button handler:', error);
      }

      // Only try to respond if we can safely do so
      if (interaction.deferred && !interaction.replied) {
        try {
          await interaction.editReply({
            content: 'An error occurred while processing your request. Please try again later.'
          });
        } catch (e) {
          // Silently ignore response errors to prevent spam
          if (!e.code || ![10062, 10064, 10008, 40062].includes(e.code)) {
            console.error('Failed to send error message:', e);
          }
        }
      }
    }
  }
};
