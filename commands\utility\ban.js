const {
  SlashCommandBuilder,
  PermissionFlagsBits,
  EmbedBuilder
} = require('discord.js');
const { saveModLogEntry } = require('./modlog.js');

const allowedRoleIds = ['1314078903522037802', '1374162168408309931'];
const logChannelId = '1384018163330715700';

module.exports = {
  data: new SlashCommandBuilder()
    .setName('ban')
    .setDescription('Ban a user from the server.')
    .addUserOption(option =>
      option.setName('user')
        .setDescription('The user to ban.')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('reason')
        .setDescription('Reason for the ban.')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('proof')
        .setDescription('Proof for the ban (link, image, etc.)')
        .setRequired(false))
    .setDefaultMemberPermissions(PermissionFlagsBits.BanMembers),

  async execute(interaction) {
    await interaction.deferReply({ ephemeral: true });

    const memberRoles = interaction.member.roles.cache;
    const hasPermission = allowedRoleIds.some(roleId => memberRoles.has(roleId));

    if (!hasPermission) {
      return interaction.editReply({
        content: 'You do not have permission to use this command.'
      });
    }

    const targetUser = interaction.options.getUser('user');
    const reason = interaction.options.getString('reason');
    const proof = interaction.options.getString('proof') || '';
    const executor = interaction.user;

    let member;
    try {
      member = await interaction.guild.members.fetch(targetUser.id);
    } catch {
      return interaction.editReply({ content: 'User not found in this server.' });
    }

    if (!member.bannable) {
      return interaction.editReply({ content: 'I cannot ban this user due to their role being higher than mine or missing permissions.' });
    }

    // Create DM Embed
    const dmEmbed = new EmbedBuilder()
      .setTitle('You Have Been Banned From Greenville Roleplay Corporation')
      .setColor('#626CE3')
      .addFields(
        { name: 'Reason', value: reason },
        proof ? { name: 'Proof', value: proof } : null
      ).filter(Boolean)
      .setFooter({ text: `If you have any questions, please DM one of the HRS.\nSigned by ${executor.tag}` });

    try {
      await targetUser.send({ embeds: [dmEmbed] });
    } catch {
      // Ignore DM failure
    }

    try {
      await member.ban({ reason: `${reason}${proof ? ` | Proof: ${proof}` : ''}` });

      // Log the ban action to modlog
      const caseId = saveModLogEntry({
        guildId: interaction.guild.id,
        targetUserId: targetUser.id,
        moderatorId: executor.id,
        action: 'ban',
        reason: reason,
        proof: proof || null
      });

      const logchannelEmbed = new EmbedBuilder()
        .setTitle('User Banned')
        .setColor('#626CE3')
        .addFields(
          { name: 'User', value: `${targetUser.tag} (${targetUser.id})` },
          { name: 'Reason', value: reason },
          proof ? { name: 'Proof', value: proof } : null,
          { name: 'Case ID', value: `#${caseId}`, inline: true }
        ).filter(Boolean)
        .setFooter({ text: `Banned by ${executor.tag}` });

      // Send to log channel
      try {
        const logChannel = interaction.guild.channels.cache.get(logChannelId);
        if (logChannel) {
          await logChannel.send({ embeds: [logchannelEmbed] });
        }
      } catch (error) {
        console.error('Failed to send ban log to channel:', error);
      }

      // Create Server Confirmation Embed
      const confirmationEmbed = new EmbedBuilder()
        .setTitle('User Banned Successfully')
        .setColor('#626CE3')
        .addFields(
          { name: 'User', value: `${targetUser.tag} (${targetUser.id})` },
          { name: 'Reason', value: reason },
          proof ? { name: 'Proof', value: proof } : null,
          { name: 'Case ID', value: `#${caseId}`, inline: true }
        ).filter(Boolean)
        .setFooter({ text: `Banned by ${executor.tag}` });

      return interaction.editReply({ embeds: [confirmationEmbed] });

    } catch {
      return interaction.editReply({
        content: 'Failed to ban the user. Please check my permissions and role position.'
      });
    }
  },
};
