const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const path = require('path');
const fs = require('fs');
const { userDataCache } = require('../../events/profilebutton.js');

const ALLOWED_ROLE_ID = '1314078957963837450';
const ALLOWED_ROLE_ID2 = '1314078977651904583';
const ALLOWED_ROLE_ID3 = '1384290870894923897';
const LOG_CHANNEL_ID = '1371687298772504576';

module.exports = {
  data: new SlashCommandBuilder()
    .setName('ticket')
    .setDescription('Issue a ticket to a user')
    .addUserOption(option =>
      option.setName('user')
        .setDescription('The user to issue the ticket to')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('offense')
        .setDescription('The offense committed')
        .setRequired(true))
    .addIntegerOption(option =>
      option.setName('fine')
        .setDescription('The fine amount')
        .setRequired(true)),

  async execute(interaction) {
    try {
      const member = interaction.member;
      if (
        !member.roles.cache.has(ALLOWED_ROLE_ID) &&
        !member.roles.cache.has(ALLOWED_ROLE_ID2) &&
        !member.roles.cache.has(ALLOWED_ROLE_ID3)
      ) {
        return await interaction.reply({
          content: 'You do not have permission to use this command.',
          ephemeral: true
        });
      }

      const user = interaction.options.getUser('user');
      const offense = interaction.options.getString('offense');
      const fine = interaction.options.getInteger('fine');

      const ticketData = {
        offense,
        price: fine,
        count: 1,
        date: new Date(),
        issuedBy: interaction.user.id
      };

      const ticketsDir = path.join(__dirname, '..', '..', 'data', 'tickets');
      const ticketPath = path.join(ticketsDir, `${user.id}.json`);

      if (!fs.existsSync(ticketsDir)) {
        fs.mkdirSync(ticketsDir, { recursive: true });
      }

      let tickets = [];
      try {
        if (fs.existsSync(ticketPath)) {
          tickets = JSON.parse(fs.readFileSync(ticketPath, 'utf8'));
        }
      } catch (error) {
        console.error('Error reading tickets file:', error);
      }

      tickets.push(ticketData);
      fs.writeFileSync(ticketPath, JSON.stringify(tickets, null, 2));

      if (userDataCache?.tickets) {
        userDataCache.tickets.delete(user.id);
      }

      // Embed shown to the person who runs the command (ephemeral)
      await interaction.reply({
        content: `Ticket issued to ${user.tag} successfully.`,
        ephemeral: true
      });

      // DM embed for the user who got ticketed
      const dmEmbed = new EmbedBuilder()
        .setColor('#626CE3')
        .setTitle('You have received a Ticket in SFRE')
        .setDescription(
          `**Offense:** ${offense}\n` +
          `**Fine:** $${fine}\n\n` +
          `If you believe this was a mistake, please contact a high-ranking staff member.\n\n` +
          `**Issued by:** ${interaction.user.tag}`
        )
        .setTimestamp()
        .setFooter({
          text: 'Greenville Roleplay Corporation',
          iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
        });

      try {
        await user.send({ embeds: [dmEmbed] });
      } catch (error) {
        console.warn(`Could not DM ${user.tag}:`, error.code);
        // Gracefully handle DM block or privacy error (DiscordAPIError 50007)
      }

      // Log embed to public channel
      const logEmbed = new EmbedBuilder()
        .setColor('#626CE3')
        .setTitle('Ticket Issued')
        .addFields(
          { name: 'User', value: user.toString(), inline: true },
          { name: 'Offense', value: offense, inline: true },
          { name: 'Fine', value: `$${fine}`, inline: true },
          { name: 'Issued By', value: interaction.user.toString() }
        )
        .setTimestamp()
        .setFooter({
          text: 'Greenville Roleplay Corporation',
          iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
        });

      const logChannel = await interaction.client.channels.fetch(LOG_CHANNEL_ID);
      if (logChannel && logChannel.isTextBased()) {
        await logChannel.send({ embeds: [logEmbed] });
      }

    } catch (error) {
      console.error('Error issuing ticket:', error);
      if (!interaction.replied) {
        await interaction.reply({
          content: 'An error occurred while issuing the ticket.',
          ephemeral: true
        });
      }
    }
  },
};
