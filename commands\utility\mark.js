const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('mark')
        .setDescription('Give a mark to a user')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to mark')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('mark')
                .setDescription('Choose which mark to give')
                .setRequired(true)
                .addChoices(
                    { name: 'Mark 1', value: 'mark1' },
                    { name: 'Mark 2', value: 'mark2' },
                    { name: 'Mark 3', value: 'mark3' }
                ))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for the mark')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('proof')
                .setDescription('Proof of the incident (link, message, etc.)')
                .setRequired(false)),

    async execute(interaction) {
        const staffRoleId = '1314078903522037802'; 
        const markRoles = {
            'mark1': '1354221125160145056',
            'mark2': '1354221124312891422',
            'mark3': '1354221123977482471'
        };

        try {
            await interaction.deferReply({ ephemeral: true });

            if (!interaction.member.roles.cache.has(staffRoleId)) {
                return interaction.editReply('You do not have permission to use this command.');
            }

            const targetUser = interaction.options.getUser('user');
            const reason = interaction.options.getString('reason');
            const proof = interaction.options.getString('proof');
            const markType = interaction.options.getString('mark');
            const roleId = markRoles[markType];

            if (!roleId) {
                return interaction.editReply('Invalid mark selected.');
            }

            const member = await interaction.guild.members.fetch(targetUser.id).catch(() => null);
            if (!member) {
                return interaction.editReply('The specified user is not in the server.');
            }

            if (member.roles.cache.has(roleId)) {
                return interaction.editReply(`${targetUser} already has this mark.`);
            }

            await member.roles.add(roleId);

            const dmEmbed = new EmbedBuilder()
                .setTitle('GVRC | Mark Given')
                .setColor(0x626CE3)
                .setDescription(
                    `You have been marked by ${interaction.user}.\n\n**Reason:** ${reason}` +
                    (proof ? `\n\n**Proof:** ${proof}` : '') +
                    `\n\nIf you have any questions, please DM one of the HRS.\n\nSigned by\n${interaction.user}`
                )
                .setTimestamp()
                .setFooter({
                    text: 'Greenville Roleplay Corporation',
                    iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
                });

            await targetUser.send({ embeds: [dmEmbed] }).catch();

            await interaction.editReply(`Successfully marked ${targetUser} with ${markType}. They have been notified via DM.`);
        } catch (error) {
            await interaction.editReply('An error occurred while processing the command.');
        }
    },
};
