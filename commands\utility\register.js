const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');

const dataDirPath = path.join(__dirname, '../../data/vehicleData');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('register')
    .setDescription('Register your vehicle.')
    .addIntegerOption(option =>
      option.setName('year')
        .setDescription('Vehicle Year')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('make')
        .setDescription('Vehicle Make')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('model')
        .setDescription('Vehicle Model')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('color')
        .setDescription('Vehicle Color')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('number-plate')
        .setDescription('Vehicle Number Plate')
        .setRequired(true)),

  async execute(interaction) {
    try {
      await interaction.deferReply({ ephemeral: true });
      
      const year = interaction.options.getInteger('year');
      const make = interaction.options.getString('make');
      const model = interaction.options.getString('model');
      const color = interaction.options.getString('color');
      const numberPlate = interaction.options.getString('number-plate');
      const userId = interaction.user.id;

      // Ensure data directory exists
      if (!fs.existsSync(dataDirPath)) {
        fs.mkdirSync(dataDirPath, { recursive: true });
        console.log(`Created vehicle data directory: ${dataDirPath}`);
      }

      const userFilePath = path.join(dataDirPath, `${userId}.json`);
      let userData;

      // Load existing data if file exists
      if (fs.existsSync(userFilePath)) {
        try {
          const fileData = fs.readFileSync(userFilePath, 'utf8');
          userData = JSON.parse(fileData);
          
          // Handle different data formats
          if (Array.isArray(userData)) {
            // Convert old format to new format
            userData = {
              lastUpdated: new Date().toISOString(),
              items: [...userData],
              metadata: {
                type: "vehicles",
                version: "1.0"
              }
            };
            console.log(`Converted old format to new format for user ${userId}`);
          } else if (!userData.items) {
            // Fix missing items array
            userData.items = [];
            userData.lastUpdated = new Date().toISOString();
            userData.metadata = {
              type: "vehicles",
              version: "1.0"
            };
          }
        } catch (error) {
          console.error(`Error parsing existing vehicle data for user ${userId}:`, error);
          // Create new data structure if parsing fails
          userData = {
            lastUpdated: new Date().toISOString(),
            items: [],
            metadata: {
              type: "vehicles",
              version: "1.0"
            }
          };
        }
      } else {
        // Create new data structure if file doesn't exist
        userData = {
          lastUpdated: new Date().toISOString(),
          items: [],
          metadata: {
            type: "vehicles",
            version: "1.0"
          }
        };
        console.log(`Creating new vehicle data file for user ${userId}`);
      }

      // Add new vehicle to items array
      userData.items.push({
        year,
        make,
        model,
        color,
        numberPlate,
        registeredAt: new Date().toISOString()
      });

      // Update lastUpdated timestamp
      userData.lastUpdated = new Date().toISOString();

      // Save to file
      try {
        fs.writeFileSync(userFilePath, JSON.stringify(userData, null, 2), 'utf8');
        console.log(`Saved vehicle data for user ${userId}, total vehicles: ${userData.items.length}`);
        
        // Update client cache if available
        if (interaction.client.vehicleData) {
          interaction.client.vehicleData[userId] = userData.items;
        }
      } catch (writeError) {
        console.error(`Error writing vehicle data for user ${userId}:`, writeError);
        await interaction.editReply({
          content: 'An error occurred while saving your vehicle data. Please try again.',
          ephemeral: true
        });
        return;
      }

      // Confirm registration to user
      const embed = new EmbedBuilder()
        .setColor(`#626CE3`)
        .setTitle('Vehicle Registered')
        .setDescription(`Your ${year} ${make} ${model} has been successfully registered.`)
        .addFields(
          { name: 'Color', value: color, inline: true },
          { name: 'Number Plate', value: numberPlate, inline: true },
          { name: 'Total Vehicles', value: `${userData.items.length}`, inline: true }
        )
        .setFooter({ text: 'Use /profile to view all your registered vehicles' });

      await interaction.editReply({ embeds: [embed], ephemeral: true });

  
  
  

    } catch (error) {
      console.error('Error in register command:', error);
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: 'An error occurred while registering your vehicle.',
            ephemeral: true
          });
        } else {
          await interaction.editReply({
            content: 'An error occurred while registering your vehicle.',
            ephemeral: true
          });
        }
      } catch (replyError) {
        console.error('Error sending error message:', replyError);
      }
    }
  },
};
