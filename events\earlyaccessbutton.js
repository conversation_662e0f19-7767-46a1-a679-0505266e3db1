const { InteractionType } = require('discord.js');
const fs = require('fs');
const path = require('path');

// ✅ Define the JSON file path at the top
const jsonFilePath = path.join(__dirname, '..', 'data', 'earlyAccessLinks.json');

// ✅ Function to ensure the JSON file exists and initialize with default data
function initializeJsonFile() {
    try {
        // Create data directory if it doesn't exist
        const dataDir = path.join(__dirname, '..', 'data');
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        // Create or read JSON file
        let links = {};
        if (fs.existsSync(jsonFilePath)) {
            const data = fs.readFileSync(jsonFilePath, 'utf8');
            try {
                links = JSON.parse(data);
            } catch (error) {
                console.error('Error parsing existing JSON file:', error);
            }
        }

        // Write back to file if it doesn't exist
        if (!fs.existsSync(jsonFilePath)) {
            fs.writeFileSync(jsonFilePath, JSON.stringify(links, null, 2), 'utf8');
        }

        return links;
    } catch (error) {
        console.error('Error initializing JSON file:', error);
        return {};
    }
}

// ✅ Initialize the JSON file when the bot starts
const links = initializeJsonFile();

module.exports = {
    name: 'interactionCreate',
    async execute(interaction) {
        if (!interaction.isButton()) return;

        if (interaction.customId === 'early_access_link') {
            try {
                await interaction.deferReply({ ephemeral: true });

                // Check permissions first
                const staffRoleId = '1362775746329841796';
                const earlyAccessRoleId1 = '1363655568308572332';
                const earlyAccessRoleId2 = '1363765919939166279';
                const earlyAccessRoleId3 = '1363656250914508930';
                const earlyAccessRoleId4 = '1375448184087642222';
                const earlyAccessRoleId5 = '1314078957963837450';

                const hasPermission =
                    interaction.member.roles.cache.has(staffRoleId) ||
                    interaction.member.roles.cache.has(earlyAccessRoleId1) ||
                    interaction.member.roles.cache.has(earlyAccessRoleId2) ||
                    interaction.member.roles.cache.has(earlyAccessRoleId3) ||
                    interaction.member.roles.cache.has(earlyAccessRoleId4) ||
                    interaction.member.roles.cache.has(earlyAccessRoleId5);

                if (!hasPermission) {
                    return await interaction.editReply({
                        content: 'You do not have permission to click on this button!',
                    });
                }

                // ✅ Read current links (fresh read every time)
                let currentLinks = {};
                try {
                    if (fs.existsSync(jsonFilePath)) {
                        const data = fs.readFileSync(jsonFilePath, 'utf8');
                        currentLinks = JSON.parse(data);
                    }
                } catch (error) {
                    console.error('Error reading links file:', error);
                }

                const link = currentLinks[interaction.message.id];

                if (!link) {
                    return await interaction.editReply({
                        content: 'This early access link has expired or is no longer available.',
                    });
                }

                await interaction.editReply({
                    content: `**Early-Access:** ${link}`,
                });
            } catch (error) {
                console.error('Error handling early access button:', error);
                try {
                    if (!interaction.replied) {
                        await interaction.reply({
                            content: 'An error occurred while processing your request.',
                            ephemeral: true,
                        });
                    } else {
                        await interaction.editReply({
                            content: 'An error occurred while processing your request.',
                        });
                    }
                } catch (e) {
                    console.error('Error sending error message:', e);
                }
            }
        }
    },
};
