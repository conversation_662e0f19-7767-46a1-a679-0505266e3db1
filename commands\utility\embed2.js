const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed2')
    .setDescription('Displays server information and rules'),

  async execute(interaction) {
    const embedColor = '#626CE3';

    // Staff role ID check
    const staffRoleId = '1314078903522037802';
    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    // Initial ephemeral confirmation
    await interaction.reply({ content: 'Embed2 Message Released!', ephemeral: true });

    // Create the embed
    const infoEmbed = new EmbedBuilder()
      .setColor(embedColor)
      .setTitle('Information')
      .setDescription(
        ' > **Greenville Roleplay Corporation** is a roleplay server on ROBLOX, based in Greenville, Wisconsin. We aim to provide realistic and immersive roleplay experiences in a well-organized environment. Our server features a professional staff team and a user-friendly, interactive setup. Enjoy various roleplay opportunities, including real estate, career services, and much more.\n\n' +
        '> Below are the links to affiliated servers within the Greenville Roleplay Corporation. If you have any questions, feel free to open a ticket in the <#1330291387987660922> channel.\n\n' +
        '<:1370859052023681024:1371683799129129121> **Informational Links**\n' +
        '[GVRC](https://discord.gg/greenvillecorporation)\n' +
        '[SFRPC](https://discord.gg/gvserver)\n' +
        '[Banned Vehicles List](https://docs.google.com/document/d/1rlLaBTOebGPML9rCmA61PeC48vlzXPIpOC1i3Qrxe2E/edit?tab=t.0)\n\n' +
        '<:1370859052023681024:1371683799129129121> **External Links**\n' +
        '[GVRC Roblox Group](https://www.roblox.com/communities/36056203/Greenville-Roleplay-Corporation#!/about)\n' +
        '[Career Center](https://discord.gg/hjb4s6vRX6)'
      )
      .setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
      });

    // Dropdown menu
    const menu = new ActionRowBuilder().addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('embed2-menu')
        .setPlaceholder('Click here for more information')
        .addOptions([
          {
            label: 'Server Rules',
            description: 'Click this option to view our server rules',
            value: 'roleplay-info',
          },
          {
            label: 'Roleplay Information',
            description: 'Click this option to view the roleplay information',
            value: 'roleplay-info2',
          },


        ])
    );

    // Send message
    await interaction.channel.send({ 
      embeds: [infoEmbed], 
      components: [menu] 
    });

    // Log to command log channel
    const logChannelId = '1334707231555452952';
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
      const logEmbed = new EmbedBuilder()
        .setTitle('Command Executed')
        .setDescription('The `/embed2` command was executed.')
        .addFields(
          { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
          { name: 'User ID', value: `${interaction.user.id}`, inline: true },
          { name: 'Channel', value: `${interaction.channel.name}`, inline: true }
        )
        .setColor('#626CE3')
        .setTimestamp();

      await logChannel.send({ embeds: [logEmbed] });
    }
  },
};
