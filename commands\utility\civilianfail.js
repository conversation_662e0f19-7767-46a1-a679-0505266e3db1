const { SlashCommandBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('civilianfail')
        .setDescription('Reject a civilian application.')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('User whose application is being rejected')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for rejection')
                .setRequired(true)),

    async execute(interaction) {
        const allowedRole = '1385051083684184114';
        if (!interaction.member.roles.cache.has(allowedRole)) {
            return interaction.reply({ content: 'You do not have permission to use this command.', ephemeral: true });
        }

        const user = interaction.options.getUser('user');
        const reason = interaction.options.getString('reason');
        const logChannelId = '1371687466523820156';

        try {
            const logChannel = interaction.guild.channels.cache.get(logChannelId);
            if (logChannel) {
                await logChannel.send({
                    content: `<@${user.id}> **Civilian Application Failed** Civilian Application has been rejected for the following reason: ${reason}`
                });
            }

            await interaction.reply({ content: `Successfully rejected ${user.tag}'s application.`, ephemeral: true });
        } catch (err) {
            console.error(err);
            await interaction.reply({ content: 'An error occurred while processing the request.', ephemeral: true });
        }
    }
};
