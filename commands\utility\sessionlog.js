const { <PERSON>lash<PERSON>ommandBuilder, EmbedBuilder } = require('discord.js');
const path = require('path');
const fs = require('fs');

// Ensure the data directory exists
const sessionLogsPath = path.join(__dirname, '../../data/sessionLogs');
if (!fs.existsSync(sessionLogsPath)) {
  fs.mkdirSync(sessionLogsPath, { recursive: true });
}

function countSessionsByType(userId) {
  const filePath = path.join(sessionLogsPath, `${userId}.json`);
  try {
    if (!fs.existsSync(filePath)) {
      return { hosts: 0, cohosts: 0 };
    }
    
    const sessions = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    return {
      hosts: sessions.filter(log => log.hostType === 'Host').length,
      cohosts: sessions.filter(log => log.hostType === 'Co-host').length
    };
  } catch (error) {
    console.error('Error counting sessions:', error);
    return { hosts: 0, cohosts: 0 };
  }
}

function saveSessionLog(userId, sessionData) {
  const filePath = path.join(sessionLogsPath, `${userId}.json`);
  try {
    let existingLogs = [];
    
    if (fs.existsSync(filePath)) {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      try {
        existingLogs = JSON.parse(fileContent);
        
        // Ensure existingLogs is an array
        if (!Array.isArray(existingLogs)) {
          console.error('Existing logs is not an array, resetting to empty array');
          existingLogs = [];
        }
      } catch (parseError) {
        console.error('Error parsing existing logs, resetting to empty array:', parseError);
        existingLogs = [];
      }
    }

    // Ensure hostType is properly set
    sessionData.hostType = sessionData.hostType === 'Co-host' ? 'Co-host' : 'Host';
    
    existingLogs.push(sessionData);
    fs.writeFileSync(filePath, JSON.stringify(existingLogs, null, 2));
    
    // Clear cache if using the cache system from staffprofilebutton.js
    if (global.sessionLogsCache && global.sessionLogsCache.delete) {
      global.sessionLogsCache.delete(userId);
    }
    
    return true;
  } catch (error) {
    console.error('Error saving session log:', error);
    return false;
  }
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName('sessionlog')
    .setDescription('Log session details')
    .addStringOption(option =>
      option.setName('host-type')
        .setDescription('Type of host')
        .setRequired(true)
        .addChoices(
          { name: 'Host', value: 'Host' },
          { name: 'Co-host', value: 'Co-host' }
        ))
    .addStringOption(option =>
      option.setName('date')
        .setDescription('Date of the session (e.g., 2024-02-25)')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('duration')
        .setDescription('How long was the session (e.g., 2 hours)')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('co-hosts')
        .setDescription('List of co-hosts (if any)')
        .setRequired(false)),

  async execute(interaction) {
    try {
      const staffRoleId = '1362775746329841796';
      const logChannelId = '1384018163330715700';

      if (!interaction.member.roles.cache.has(staffRoleId)) {
        await interaction.reply({ 
          content: 'You do not have permission to use this command!', 
          ephemeral: true 
        });
        return;
      }

      const hostType = interaction.options.getString('host-type');
      const date = interaction.options.getString('date');
      const duration = interaction.options.getString('duration');
      const coHosts = interaction.options.getString('co-hosts') || 'None';

      // Create session data object
      const sessionData = {
        userId: interaction.user.id,
        hostType,
        date,
        duration,
        coHosts,
        timestamp: new Date().toISOString()
      };

      // Save to JSON file
      const saved = saveSessionLog(interaction.user.id, sessionData);

      // Get updated session counts
      const sessionCounts = countSessionsByType(interaction.user.id);

      const embed = new EmbedBuilder()
        .setTitle('Session Log')
        .setDescription(`Session details logged by ${interaction.user}`)
        .addFields(
          { name: 'Host Type', value: hostType, inline: true },
          { name: 'Date', value: date, inline: true },
          { name: 'Duration', value: duration, inline: true },
          { name: 'Co-Hosts', value: coHosts, inline: false },
          { name: 'Total Sessions', value: `Hosted: ${sessionCounts.hosts} | Co-hosted: ${sessionCounts.cohosts}`, inline: false }
        )
        .setColor('#02a0dd')
        .setTimestamp()
        .setFooter({
          text: 'Greenville Roleplay Corporation',
          iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
        });

      // Send confirmation to user
      await interaction.reply({ 
        content: saved 
          ? 'Session log has been recorded and saved!' 
          : 'Session log has been recorded but there was an error saving to history.',
        ephemeral: true 
      });

      // Send log to the logging channel
      const logChannel = interaction.client.channels.cache.get(logChannelId);
      if (logChannel) {
        await logChannel.send({ embeds: [embed] });
      }

    } catch (error) {
      console.error('Error in sessionlog command:', error);
      await interaction.reply({ 
        content: 'There was an error while executing this command!', 
        ephemeral: true 
      });
    }
  }
};

