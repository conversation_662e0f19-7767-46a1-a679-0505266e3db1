const { Events } = require('discord.js');

const ROLE_ID = '1335364652497895424';
const processingUsers = new Set();

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isButton() || interaction.customId !== 'session_ping') return;

        if (processingUsers.has(interaction.user.id)) {
            try {
                await interaction.deferUpdate();
            } catch {}
            return;
        }

        processingUsers.add(interaction.user.id);

        try {
            // Defer the reply immediately
            await interaction.deferUpdate();

            if (!interaction.guild || !interaction.member) {
                await interaction.followUp({ 
                    content: 'Thiscommand can only be used in a server.', 
                    ephemeral: true 
                }).catch(() => {});
                return;
            }

            const role = interaction.guild.roles.cache.get(ROLE_ID);
            if (!role) {
                await interaction.followUp({ 
                    content: 'The session ping role could not be found.', 
                    ephemeral: true 
                }).catch(() => {});
                return;
            }

            const hasRole = interaction.member.roles.cache.has(ROLE_ID);

            try {
                if (hasRole) {
                    await interaction.member.roles.remove(role);
                } else {
                    await interaction.member.roles.add(role);
                }

                await interaction.followUp({
                    content: hasRole 
                        ? `✅ Successfully removed the ${role.name} role.`
                        : `✅ Successfully added the ${role.name} role.`,
                    ephemeral: true
                }).catch(() => {});

            } catch (roleError) {
                await interaction.followUp({
                    content: '⚠️ An error occurred while updating your role.',
                    ephemeral: true
                }).catch(() => {});
            }

        } catch {
            // Silently fail
        } finally {
            setTimeout(() => {
                processingUsers.delete(interaction.user.id);
            }, 1000);
        }
    }
};
