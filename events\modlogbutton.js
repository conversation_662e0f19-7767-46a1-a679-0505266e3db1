const { Events, Embed<PERSON>uild<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Helper function to get modlog data
function getModLogData() {
  const filePath = path.join(__dirname, '../data/modlogs.json');
  
  if (!fs.existsSync(filePath)) {
    return [];
  }
  
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading modlog data:', error);
    return [];
  }
}

// Helper function to format action type
function formatAction(action) {
  const actionMap = {
    'ban': 'Ban',
    'unban': 'Unban',
    'kick': 'Kick',
    'warn': 'Warning',
    'unwarn': 'Warning Removed',
    'mute': 'Mute',
    'unmute': 'Unmute',
    'timeout': 'Timeout',
    'untimeout': 'Remove Timeout'
  };
  return actionMap[action] || action;
}

// Helper function to create modlog embed
function createModLogEmbed(logs, page, totalPages, targetUser, guildName) {
  const embed = new EmbedBuilder()
    .setColor('#626CE3')
    .setTimestamp()
    .setFooter({
      text: `Page ${page}/${totalPages} • Greenville Roleplay Corporation`,
      iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
    });

  if (targetUser) {
    embed.setTitle(`Moderation Logs for ${targetUser.tag}`)
         .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }));
  } else {
    embed.setTitle(`Server Moderation Logs`)
         .setDescription(`Showing moderation logs for **${guildName}**`);
  }

  if (logs.length === 0) {
    embed.setDescription('No moderation logs found.');
    return embed;
  }

  const startIndex = (page - 1) * 5;
  const endIndex = Math.min(startIndex + 5, logs.length);
  const pageEntries = logs.slice(startIndex, endIndex);

  pageEntries.forEach(logEntry => {
    const timestamp = new Date(logEntry.timestamp);
    const formattedDate = `<t:${Math.floor(timestamp.getTime() / 1000)}:F>`;
    
    let fieldValue = `**Moderator:** <@${logEntry.moderatorId}>\n**Reason:** ${logEntry.reason}`;
    if (logEntry.proof) {
      fieldValue += `\n**Proof:** ${logEntry.proof}`;
    }
    fieldValue += `\n**Date:** ${formattedDate}`;

    embed.addFields({
      name: `Case #${logEntry.caseId} - ${formatAction(logEntry.action)}`,
      value: fieldValue,
      inline: false
    });
  });

  return embed;
}

// Helper function to check access permissions
function hasModlogAccess(interaction, targetUserId, requesterId) {
  const staffRoleId = '1314078903522037802'; // Staff role ID
  
  // Allow if user is viewing their own modlog
  if (targetUserId === requesterId) {
    return true;
  }
  
  // Allow if requester is staff
  if (interaction.member.roles.cache.has(staffRoleId)) {
    return true;
  }
  
  return false;
}

module.exports = {
  name: Events.InteractionCreate,
  async execute(interaction) {
    if (!interaction.isButton()) return;

    const customId = interaction.customId;
    if (!customId.startsWith('show_modlog_') && 
        !customId.startsWith('modlog_profile_prev_') && 
        !customId.startsWith('modlog_profile_next_')) {
      return;
    }

    try {
      // Prevent timeout errors by deferring immediately
      if (!interaction.deferred && !interaction.replied) {
        await interaction.deferUpdate();
      }

      if (customId.startsWith('show_modlog_')) {
        // Parse the custom ID: show_modlog_{targetUserId}_{requesterId}
        const parts = customId.split('_');
        const targetUserId = parts[2];
        const requesterId = parts[3];

        // Check access permissions
        if (!hasModlogAccess(interaction, targetUserId, requesterId)) {
          await interaction.followUp({
            content: 'You do not have permission to view this user\'s moderation logs.',
            ephemeral: true
          }).catch(console.error);
          return;
        }

        // Get modlog data
        const allLogs = getModLogData();
        const userLogs = allLogs.filter(log => 
          log.guildId === interaction.guild.id && 
          log.targetUserId === targetUserId
        );

        // Sort by timestamp (newest first)
        userLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        const totalPages = Math.ceil(userLogs.length / 5) || 1;
        const currentPage = 1;

        const targetUser = await interaction.client.users.fetch(targetUserId).catch(() => null);
        if (!targetUser) {
          await interaction.followUp({
            content: 'Could not find the target user.',
            ephemeral: true
          }).catch(console.error);
          return;
        }

        const embed = createModLogEmbed(userLogs, currentPage, totalPages, targetUser, interaction.guild.name);

        const row = new ActionRowBuilder()
          .addComponents(
            new ButtonBuilder()
              .setCustomId(`modlog_profile_prev_${targetUserId}_${requesterId}_${currentPage}`)
              .setLabel('Previous')
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(currentPage === 1),
            new ButtonBuilder()
              .setCustomId(`modlog_profile_next_${targetUserId}_${requesterId}_${currentPage}`)
              .setLabel('Next')
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(currentPage === totalPages)
          );

        await interaction.followUp({
          embeds: [embed],
          components: [row],
          ephemeral: true
        }).catch(console.error);

      } else if (customId.includes('_prev_') || customId.includes('_next_')) {
        // Handle pagination
        const parts = customId.split('_');
        const action = parts[2]; // prev or next
        const targetUserId = parts[3];
        const requesterId = parts[4];
        const currentPage = parseInt(parts[5]);

        // Check access permissions again
        if (!hasModlogAccess(interaction, targetUserId, requesterId)) {
          await interaction.editReply({
            content: 'You do not have permission to view this user\'s moderation logs.',
            embeds: [],
            components: []
          }).catch(console.error);
          return;
        }

        // Get modlog data
        const allLogs = getModLogData();
        const userLogs = allLogs.filter(log => 
          log.guildId === interaction.guild.id && 
          log.targetUserId === targetUserId
        );

        // Sort by timestamp (newest first)
        userLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        const totalPages = Math.ceil(userLogs.length / 5) || 1;
        let newPage = currentPage;

        if (action === 'prev' && currentPage > 1) {
          newPage = currentPage - 1;
        } else if (action === 'next' && currentPage < totalPages) {
          newPage = currentPage + 1;
        }

        const targetUser = await interaction.client.users.fetch(targetUserId).catch(() => null);
        if (!targetUser) {
          await interaction.editReply({
            content: 'Could not find the target user.',
            embeds: [],
            components: []
          }).catch(console.error);
          return;
        }

        const embed = createModLogEmbed(userLogs, newPage, totalPages, targetUser, interaction.guild.name);

        const row = new ActionRowBuilder()
          .addComponents(
            new ButtonBuilder()
              .setCustomId(`modlog_profile_prev_${targetUserId}_${requesterId}_${newPage}`)
              .setLabel('Previous')
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(newPage === 1),
            new ButtonBuilder()
              .setCustomId(`modlog_profile_next_${targetUserId}_${requesterId}_${newPage}`)
              .setLabel('Next')
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(newPage === totalPages)
          );

        await interaction.editReply({
          embeds: [embed],
          components: [row]
        }).catch(console.error);
      }

    } catch (error) {
      console.error('Error in modlog button handler:', error);
      
      // Prevent 10062 errors by checking interaction state
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: 'An error occurred while processing your request.',
            ephemeral: true
          });
        } else if (interaction.deferred) {
          await interaction.editReply({
            content: 'An error occurred while processing your request.',
            embeds: [],
            components: []
          });
        }
      } catch (replyError) {
        console.error('Error sending error message:', replyError);
      }
    }
  }
};
