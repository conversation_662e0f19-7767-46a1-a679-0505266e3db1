const { Events } = require('discord.js');
const { 
    <PERSON><PERSON><PERSON><PERSON><PERSON>, 
    TextInputBuilder, 
    TextInputStyle, 
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    EmbedBuilder,
    PermissionsBitField,
    ChannelType,
    MessageFlags
} = require('discord.js');
const path = require('path');
const fs = require('fs');

// Define the transcripts directory constant
const TRANSCRIPTS_DIR = path.join(__dirname, '../transcripts');

// Create the transcripts directory if it doesn't exist
if (!fs.existsSync(TRANSCRIPTS_DIR)) {
    fs.mkdirSync(TRANSCRIPTS_DIR, { recursive: true });
}

// Track interaction states to prevent duplicate responses
const interactionStates = new Map();

// Safe interaction response wrapper
async function safeReply(interaction, options, type = 'reply') {
    try {
        // Check if we've already handled this interaction
        const interactionId = interaction.id;
        if (interactionStates.has(interactionId)) {
            // Don't log this to avoid console spam
            return false;
        }

        // Mark this interaction as being handled
        interactionStates.set(interactionId, { handling: true, timestamp: Date.now() });

        // Check if the interaction is still valid
        if (!interaction.isRepliable()) {
            interactionStates.set(interactionId, { handled: true, success: false, timestamp: Date.now() });
            return false;
        }

        // Convert ephemeral to flags if present
        if (options && options.ephemeral) {
            options.flags = MessageFlags.Ephemeral;
            delete options.ephemeral;
        }

        // Handle different response types
        let result;
        switch (type) {
            case 'reply':
                if (interaction.replied) {
                    interactionStates.set(interactionId, { handled: true, success: true, timestamp: Date.now() });
                    return true;
                }
                if (interaction.deferred) {
                    result = await interaction.editReply(options).catch(() => null);
                } else {
                    result = await interaction.reply(options).catch(() => null);
                }
                break;
            case 'defer':
                if (interaction.deferred || interaction.replied) {
                    interactionStates.set(interactionId, { handled: true, success: true, timestamp: Date.now() });
                    return true;
                }
                result = await interaction.deferReply(options).catch(() => null);
                break;
            case 'update':
                result = await interaction.update(options).catch(() => null);
                break;
            case 'followUp':
                result = await interaction.followUp(options).catch(() => null);
                break;
            case 'showModal':
                result = await interaction.showModal(options).catch(() => null);
                break;
        }

        // Mark as successfully handled
        interactionStates.set(interactionId, { handled: true, success: !!result, timestamp: Date.now() });
        return !!result;
    } catch (error) {
        console.error(`Error in safeReply (${type}):`, error);
        interactionStates.set(interaction.id, { handled: true, success: false, error, timestamp: Date.now() });
        return false;
    }
}

// Clean up old interaction states periodically
setInterval(() => {
    const now = Date.now();
    const EXPIRY_TIME = 10 * 60 * 1000; // 10 minutes
    
    interactionStates.forEach((state, id) => {
        if (state.timestamp && now - state.timestamp > EXPIRY_TIME) {
            interactionStates.delete(id);
        }
    });
}, 15 * 60 * 1000); // Run every 15 minutes

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        try {
            const logChannelId = '1381460621744406629';
            const supportRoleId = '1363682766855606272';

            // Handle select menu interactions
            if (interaction.isStringSelectMenu() && interaction.customId === 'supportOptions') {
                const selectedValue = interaction.values[0];

                if (selectedValue === 'mr') {
                    const modal = new ModalBuilder()
                        .setCustomId('ticketReasonModal')
                        .setTitle('Support Ticket Reason');

                    const reasonInput = new TextInputBuilder()
                        .setCustomId('reasonInput')
                        .setLabel('Please describe your issue')
                        .setStyle(TextInputStyle.Paragraph)
                        .setPlaceholder('Describe your issue in detail...')
                        .setRequired(true)
                        .setMaxLength(1000);

                    modal.addComponents(new ActionRowBuilder().addComponents(reasonInput));
                    await safeReply(interaction, modal, 'showModal');
                    return;
                } else if (selectedValue === 'ss') {
                    const modal = new ModalBuilder()
                        .setCustomId('staffSupportModal')
                        .setTitle('Staff Support Request');

                    const reasonInput = new TextInputBuilder()
                        .setCustomId('staffReasonInput')
                        .setLabel('Please describe your issue')
                        .setStyle(TextInputStyle.Paragraph)
                        .setPlaceholder('Describe your issue in detail...')
                        .setRequired(true)
                        .setMinLength(2)
                        .setMaxLength(4000);

                    modal.addComponents(new ActionRowBuilder().addComponents(reasonInput));
                    await safeReply(interaction, modal, 'showModal');
                    return;
                } else if (selectedValue === 'pr') {
                    const modal = new ModalBuilder()
                        .setCustomId('partnershipModal')
                        .setTitle('Partnership Request');

                    const userInput = new TextInputBuilder()
                        .setCustomId('userInput')
                        .setLabel('Your Discord Username')
                        .setStyle(TextInputStyle.Short)
                        .setRequired(true);

                    const dateInput = new TextInputBuilder()
                        .setCustomId('dateInput')
                        .setLabel('When was your server created?')
                        .setStyle(TextInputStyle.Short)
                        .setRequired(true);

                    const memberCountInput = new TextInputBuilder()
                        .setCustomId('memberCountInput')
                        .setLabel('How many members does your server have?')
                        .setStyle(TextInputStyle.Short)
                        .setRequired(true);

                    const serverNameInput = new TextInputBuilder()
                        .setCustomId('serverNameInput')
                        .setLabel('What is your server name?')
                        .setStyle(TextInputStyle.Short)
                        .setRequired(true);

                    modal.addComponents(
                        new ActionRowBuilder().addComponents(userInput),
                        new ActionRowBuilder().addComponents(dateInput),
                        new ActionRowBuilder().addComponents(memberCountInput),
                        new ActionRowBuilder().addComponents(serverNameInput)
                    );
                    await safeReply(interaction, modal, 'showModal');
                    return;
                }
            }

            // Handle modal submissions
            if (interaction.isModalSubmit()) {
                if (interaction.customId === 'ticketReasonModal') {
                    await handleMemberTicket(interaction, logChannelId, supportRoleId);
                    return;
                }

                if (interaction.customId === 'staffSupportModal') {
                    await handleStaffTicket(interaction, logChannelId, supportRoleId);
                    return;
                }

                if (interaction.customId === 'partnershipModal') {
                    await handlePartnershipTicket(interaction, logChannelId, supportRoleId);
                    return;
                }
            }

            // Handle button interactions
            if (interaction.isButton()) {
                if (interaction.customId === 'claimTicket') {
                    await handleTicketClaim(interaction, supportRoleId);
                    return;
                }

                const now = Date.now();
                const cooldownTime = 2000; // 2 seconds cooldown

                if (interaction.customId === 'closeTicket' || interaction.customId === 'closeStaffTicket') {
                    if (interaction.channel.cooldown && now - interaction.channel.cooldown < cooldownTime) {
                        await safeReply(interaction, { 
                            content: 'Please wait a moment before trying to close the ticket again.', 
                            ephemeral: true 
                        });
                        return;
                    }
                    interaction.channel.cooldown = now;
                    await handleCloseConfirmation(interaction);
                    return;
                }

                if (interaction.customId === 'confirmClose') {
                    if (interaction.channel.cooldown && now - interaction.channel.cooldown < cooldownTime) {
                        await safeReply(interaction, { 
                            content: 'Please wait a moment before confirming.', 
                            ephemeral: true 
                        });
                        return;
                    }
                    interaction.channel.cooldown = now;
                    await handleTicketClose(interaction, logChannelId);
                    return;
                }
            }
        } catch (error) {
            console.error('Error in ticket handler:', error);
            try {
                if (!interactionStates.has(interaction.id) || !interactionStates.get(interaction.id).handled) {
                    await safeReply(interaction, { 
                        content: 'An error occurred while processing your request. Please try again later.', 
                        ephemeral: true 
                    });
                }
            } catch (e) {
                console.error('Error sending error message:', e);
            }
        }
    }
};

async function handleStaffTicket(interaction, logChannelId, supportRoleId) {
    try {
        const reason = interaction.fields.getTextInputValue('staffReasonInput');
        
        // Create the ticket channel first before responding to the interaction
        const staffChannel = await createTicketChannel(interaction, 'staff-support', supportRoleId);
        const staffEmbed = createTicketEmbed(interaction, 'Staff Support', reason);
        
        const closeButton = createCloseButton('closeStaffTicket');
        const claimButton = new ButtonBuilder()
            .setCustomId('claimTicket')
            .setLabel('Claim Ticket')
            .setStyle(ButtonStyle.Primary);
            
        const row = new ActionRowBuilder()
            .addComponents(closeButton, claimButton);

        // Store the ticket creator ID in the channel for later use
        staffChannel.ticketCreatorId = interaction.user.id;

        // Send the initial message in the ticket channel
        await staffChannel.send({
            content: `<@${interaction.user.id}>, <@&${supportRoleId}>`,
            embeds: [staffEmbed],
            components: [row]
        }).catch(error => console.error('Error sending initial staff ticket message:', error));

        // Log the ticket creation
        await logTicketCreation(interaction, logChannelId, staffChannel, reason)
            .catch(error => console.error('Error logging staff ticket creation:', error));
        
        // Now respond to the user
        try {
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ 
                    content: `Your staff support ticket has been opened! <#${staffChannel.id}>`, 
                    flags: MessageFlags.Ephemeral 
                });
            } else if (interaction.deferred) {
                await interaction.editReply({ 
                    content: `Your staff support ticket has been opened! <#${staffChannel.id}>` 
                });
            }
        } catch (replyError) {
            console.error('Error replying to user:', replyError);
            // If we can't reply, at least the ticket was created
        }
        
        // Send a DM to the user as a backup notification
        try {
            await interaction.user.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#02a0dd')
                        .setTitle('Ticket Opened')
                        .setDescription(`Your staff support ticket has been opened in <#${staffChannel.id}>`)
                        .setTimestamp()
                ]
            }).catch(() => {
                // DM failed, but that's okay
            });
        } catch (dmError) {
            // Ignore DM errors
        }
    } catch (error) {
        console.error('Error in handleStaffTicket:', error);
        try {
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ 
                    content: 'An error occurred while creating your staff ticket, but your ticket may have been created. Please check the server.', 
                    flags: MessageFlags.Ephemeral 
                });
            } else if (interaction.deferred) {
                await interaction.editReply({ 
                    content: 'An error occurred while creating your staff ticket, but your ticket may have been created. Please check the server.' 
                });
            }
        } catch (replyError) {
            console.error('Error sending error message:', replyError);
        }
    }
}

async function handleMemberTicket(interaction, logChannelId, supportRoleId) {
    try {
        const reason = interaction.fields.getTextInputValue('reasonInput');
        
        // Create the ticket channel first before responding to the interaction
        const memberChannel = await createTicketChannel(interaction, 'member-report', supportRoleId);
        const ticketEmbed = createTicketEmbed(interaction, 'Member Report', reason);
        
        const closeButton = createCloseButton('closeTicket');
        const claimButton = new ButtonBuilder()
            .setCustomId('claimTicket')
            .setLabel('Claim Ticket')
            .setStyle(ButtonStyle.Primary);
            
        const row = new ActionRowBuilder()
            .addComponents(closeButton, claimButton);

        // Store the ticket creator ID in the channel for later use
        memberChannel.ticketCreatorId = interaction.user.id;

        // Send the initial message in the ticket channel
        await memberChannel.send({
            content: `<@${interaction.user.id}>, <@&${supportRoleId}>`,
            embeds: [ticketEmbed],
            components: [row]
        }).catch(error => console.error('Error sending initial ticket message:', error));

        // Log the ticket creation
        await logTicketCreation(interaction, logChannelId, memberChannel, reason)
            .catch(error => console.error('Error logging ticket creation:', error));
        
        // Now respond to the user
        try {
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ 
                    content: `Your report ticket has been opened! <#${memberChannel.id}>`, 
                    flags: MessageFlags.Ephemeral 
                });
            } else if (interaction.deferred) {
                await interaction.editReply({ 
                    content: `Your report ticket has been opened! <#${memberChannel.id}>` 
                });
            }
        } catch (replyError) {
            console.error('Error replying to user:', replyError);
            // If we can't reply, at least the ticket was created
        }
        
        // Send a DM to the user as a backup notification
        try {
            await interaction.user.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#02a0dd')
                        .setTitle('Ticket Opened')
                        .setDescription(`Your report ticket has been opened in <#${memberChannel.id}>`)
                        .setTimestamp()
                ]
            }).catch(() => {
                // DM failed, but that's okay
            });
        } catch (dmError) {
            // Ignore DM errors
        }
    } catch (error) {
        console.error('Error in handleMemberTicket:', error);
        try {
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ 
                    content: 'An error occurred while creating your ticket, but your ticket may have been created. Please check the server.', 
                    flags: MessageFlags.Ephemeral 
                });
            } else if (interaction.deferred) {
                await interaction.editReply({ 
                    content: 'An error occurred while creating your ticket, but your ticket may have been created. Please check the server.' 
                });
            }
        } catch (replyError) {
            console.error('Error sending error message:', replyError);
        }
    }
}

async function handlePartnershipTicket(interaction, logChannelId, supportRoleId) {
    try {
        const username = interaction.fields.getTextInputValue('userInput');
        const date = interaction.fields.getTextInputValue('dateInput');
        const memberCount = interaction.fields.getTextInputValue('memberCountInput');
        const serverName = interaction.fields.getTextInputValue('serverNameInput');
        
        // Create the ticket channel first before responding to the interaction
        const partnerChannel = await createTicketChannel(interaction, 'partnership', supportRoleId);
        
        const partnerEmbed = new EmbedBuilder()
            .setColor('#02a0dd')
            .setTitle('Partnership Request')
            .setDescription(`Hello <@${interaction.user.id}>, your partnership request ticket has been opened. Please wait for staff to assist you.`)
            .addFields(
                { name: 'Discord Username', value: username, inline: true },
                { name: 'Date', value: date, inline: true },
                { name: 'Member Count', value: memberCount, inline: true },
                { name: 'Server Name', value: serverName, inline: false }
            )
            .setFooter({ text: `Ticket opened by: ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) });

        const closeButton = createCloseButton('closeTicket');
        const claimButton = new ButtonBuilder()
            .setCustomId('claimTicket')
            .setLabel('Claim Ticket')
            .setStyle(ButtonStyle.Primary);
            
        const buttonRow = new ActionRowBuilder()
            .addComponents(closeButton, claimButton);

        // Store the ticket creator ID in the channel for later use
        partnerChannel.ticketCreatorId = interaction.user.id;

        await partnerChannel.send({
            content: `<@${interaction.user.id}>, <@&${supportRoleId}>`,
            embeds: [partnerEmbed],
            components: [buttonRow]
        }).catch(error => console.error('Error sending initial partnership ticket message:', error));

        await logTicketCreation(interaction, logChannelId, partnerChannel, 'Partnership Request')
            .catch(error => console.error('Error logging partnership ticket creation:', error));
        
        // Now respond to the user - simplified approach
        try {
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ 
                    content: `Your partnership request ticket has been opened! <#${partnerChannel.id}>`, 
                    flags: MessageFlags.Ephemeral 
                });
            } else if (interaction.deferred) {
                await interaction.editReply({ 
                    content: `Your partnership request ticket has been opened! <#${partnerChannel.id}>` 
                });
            }
        } catch (replyError) {
            console.error('Error replying to user:', replyError);
            // If we can't reply, at least the ticket was created
        }
        
        // Send a DM to the user as a backup notification
        try {
            await interaction.user.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#02a0dd')
                        .setTitle('Ticket Opened')
                        .setDescription(`Your partnership request ticket has been opened in <#${partnerChannel.id}>`)
                        .setTimestamp()
                ]
            }).catch(() => {
                // DM failed, but that's okay
            });
        } catch (dmError) {
            // Ignore DM errors
        }
    } catch (error) {
        console.error('Error in handlePartnershipTicket:', error);
        try {
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ 
                    content: 'An error occurred while creating your partnership ticket. Please try again later.',
                    flags: MessageFlags.Ephemeral
                });
            } else if (interaction.deferred) {
                await interaction.editReply({ 
                    content: 'An error occurred while creating your partnership ticket. Please try again later.'
                });
            }
        } catch (replyError) {
            console.error('Error sending error message:', replyError);
        }
    }
}

async function handleCloseConfirmation(interaction) {
    try {
        const confirmButton = new ButtonBuilder()
            .setCustomId('confirmClose')
            .setLabel('Confirm Close')
            .setStyle(ButtonStyle.Danger);

        const confirmRow = new ActionRowBuilder()
            .addComponents(confirmButton);

        await safeReply(interaction, {
            content: 'Are you sure you want to close this ticket? Click the button below to confirm.',
            components: [confirmRow],
            ephemeral: true
        });
    } catch (error) {
        console.error('Error in handleCloseConfirmation:', error);
    }
}

async function handleTicketClose(interaction, logChannelId) {
    try {
        const ticketChannel = interaction.channel;
        if (!ticketChannel) {
            await interaction.reply({ 
                content: 'Cannot find the ticket channel.',
                ephemeral: true 
            }).catch(() => {});
            return;
        }

        // Acknowledge the interaction first
        await interaction.reply({ 
            content: 'Closing ticket, please wait...',
            ephemeral: true 
        }).catch(() => {});

        // Find the ticket creator from the first message
        let ticketCreatorId = ticketChannel.ticketCreatorId;
        
        if (!ticketCreatorId) {
            // Try to find the ticket creator from the first message
            const messages = await ticketChannel.messages.fetch({ limit: 100 });
            const firstMessage = messages.last();
            
            if (firstMessage && firstMessage.embeds.length > 0) {
                const description = firstMessage.embeds[0].description;
                const userIdMatch = description?.match(/<@(\d+)>/);
                if (userIdMatch && userIdMatch[1]) {
                    ticketCreatorId = userIdMatch[1];
                }
            }
        }

        // Fetch messages and create transcript
        const messages = await ticketChannel.messages.fetch({ limit: 100 }).catch(error => {
            console.error('Error fetching messages:', error);
            return null;
        });
        
        let transcriptFilePath = null;
        
        if (messages) {
            const transcriptContent = createTranscript(messages);
            transcriptFilePath = path.join(TRANSCRIPTS_DIR, `${ticketChannel.name}-${Date.now()}.txt`);
            
            try {
                fs.writeFileSync(transcriptFilePath, transcriptContent, 'utf8');
            } catch (fsError) {
                console.error('Error writing transcript file:', fsError);
            }
        }

        // Create close embed
        const closeEmbed = new EmbedBuilder()
            .setTitle('Ticket Closed')
            .setDescription(`Your ticket in ${ticketChannel.guild.name} has been closed by ${interaction.user.tag}`)
            .setColor('#02a0dd')
            .setTimestamp();

        // Try to send DM to ticket creator
        if (ticketCreatorId) {
            try {
                const ticketCreator = await interaction.client.users.fetch(ticketCreatorId);
                
                if (transcriptFilePath) {
                    await ticketCreator.send({
                        content: 'Here is the transcript for your closed ticket:',
                        embeds: [closeEmbed],
                        files: [{ attachment: transcriptFilePath, name: 'transcript.txt' }]
                    }).catch(() => {
                        console.log(`Could not send DM to ticket creator ${ticketCreatorId}`);
                    });
                } else {
                    await ticketCreator.send({
                        embeds: [closeEmbed]
                    }).catch(() => {
                        console.log(`Could not send DM to ticket creator ${ticketCreatorId}`);
                    });
                }
            } catch (dmError) {
                console.error('Could not send DM to ticket creator:', dmError);
            }
        }

        // Also send DM to the person who closed the ticket if different
        if (interaction.user.id !== ticketCreatorId) {
            try {
                if (transcriptFilePath) {
                    await interaction.user.send({
                        content: 'Here is the transcript for the ticket you closed:',
                        embeds: [closeEmbed],
                        files: [{ attachment: transcriptFilePath, name: 'transcript.txt' }]
                    }).catch(() => {});
                }
            } catch (dmError) {
                // Ignore DM errors for the closer
            }
        }

        // Log the ticket closure
        try {
            await logTicketClose(interaction, logChannelId, ticketChannel);
        } catch (logError) {
            console.error('Could not log ticket closure:', logError);
        }

        // Send a closing message in the channel
        try {
            await ticketChannel.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#02a0dd')
                        .setTitle('Ticket Closing')
                        .setDescription(`This ticket has been closed by <@${interaction.user.id}>`)
                        .setTimestamp()
                ]
            });
        } catch (error) {
            console.error('Could not send closing message:', error);
        }

        // Delete the channel after a delay
        setTimeout(async () => {
            try {
                await ticketChannel.delete();
            } catch (deleteError) {
                console.error('Could not delete channel:', deleteError);
            }
        }, 5000);
    } catch (error) {
        console.error('Error in handleTicketClose:', error);
        try {
            await interaction.followUp({ 
                content: 'An error occurred while closing the ticket.',
                ephemeral: true 
            }).catch(() => {});
        } catch (e) {
            // Ignore follow-up errors
        }
    }
}

async function handleTicketClaim(interaction, supportRoleId) {
    try {
        if (!interaction.member.roles.cache.has(supportRoleId)) {
            await safeReply(interaction, { 
                content: 'You do not have permission to claim tickets.',
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        const message = interaction.message;
        if (!message) {
            await safeReply(interaction, { 
                content: 'Cannot find the original message.',
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        const originalEmbed = message.embeds[0];
        if (!originalEmbed) {
            await safeReply(interaction, { 
                content: 'Cannot find the ticket information.',
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        const embed = EmbedBuilder.from(originalEmbed);
        embed.setDescription(originalEmbed.description + `\n\n**Ticket claimed by:** <@${interaction.user.id}>`);

        const closeButton = new ButtonBuilder()
            .setCustomId('closeTicket')
            .setLabel('Close Ticket')
            .setStyle(ButtonStyle.Danger);

        const claimButton = new ButtonBuilder()
            .setCustomId('claimTicket')
            .setLabel('Claim Ticket')
            .setStyle(ButtonStyle.Primary)
            .setDisabled(true);

        const row = new ActionRowBuilder()
            .addComponents(closeButton, claimButton);

        await message.edit({
            embeds: [embed],
            components: [row]
        });

        // Send a separate claim message that can be referenced by the unclaim command
        await interaction.channel.send({
            content: `<@${interaction.user.id}> has claimed this ticket.`
        });

        // Update channel permissions
        const ticketCreator = interaction.channel.ticketCreatorId || 
            originalEmbed.description.match(/<@(\d+)>/)?.[1];

        if (ticketCreator) {
            await interaction.channel.permissionOverwrites.set([
                // Default permissions (everyone)
                {
                    id: interaction.guild.id,
                    deny: [PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ViewChannel],
                },
                // Support role permissions (can only view)
                {
                    id: supportRoleId,
                    allow: [PermissionsBitField.Flags.ViewChannel],
                    deny: [PermissionsBitField.Flags.SendMessages],
                },
                // Ticket claimer permissions
                {
                    id: interaction.user.id,
                    allow: [PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ViewChannel],
                },
                // Ticket creator permissions
                {
                    id: ticketCreator,
                    allow: [PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ViewChannel],
                }
            ]);
        }

        await safeReply(interaction, { 
            content: 'You have successfully claimed this ticket.',
            flags: MessageFlags.Ephemeral
        });
    } catch (error) {
        console.error('Error in handleTicketClaim:', error);
        await safeReply(interaction, { 
            content: 'An error occurred while claiming the ticket.',
            flags: MessageFlags.Ephemeral
        });
    }
}

async function createTicketChannel(interaction, prefix, supportRoleId) {
    const guild = interaction.guild;
    const ticketCount = await getNextTicketNumber(guild);
    const channelName = `${prefix}-${interaction.user.username.toLowerCase()}-${ticketCount}`;
    
    // Create the channel
    const channel = await guild.channels.create({
        name: channelName,
        type: ChannelType.GuildText,
        permissionOverwrites: [
            {
                id: guild.roles.everyone,
                deny: [PermissionsBitField.Flags.ViewChannel],
            },
            {
                id: interaction.user.id,
                allow: [PermissionsBitField.Flags.ViewChannel, PermissionsBitField.Flags.SendMessages],
            },
            {
                id: supportRoleId,
                allow: [PermissionsBitField.Flags.ViewChannel, PermissionsBitField.Flags.SendMessages],
            },
        ],
    });
    
    // Store the ticket creator ID directly on the channel object
    channel.ticketCreatorId = interaction.user.id;
    
    return channel;
}

function createTicketEmbed(interaction, title, reason) {
    return new EmbedBuilder()
        .setColor('#02a0dd')
        .setTitle(title)
        .setDescription(`Hello <@${interaction.user.id}>, your ticket has been opened. Please wait for staff to assist you.\nReason: ${reason}`)
        .setFooter({ text: `Ticket opened by: ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) });
}

function createCloseButton(customId) {
    return new ButtonBuilder()
        .setCustomId(customId)
        .setLabel('Close Ticket')
        .setStyle(ButtonStyle.Danger);
}

function createTranscript(messages) {
    let transcriptText = '=== Ticket Transcript ===\n\n';
    
    // Convert messages to array and sort chronologically
    const messageArray = Array.from(messages.values()).sort((a, b) => a.createdTimestamp - b.createdTimestamp);
    
    messageArray.forEach(msg => {
        if (!msg.author.bot) {
            const timestamp = new Date(msg.createdAt).toLocaleString();
            transcriptText += `[${timestamp}] ${msg.author.username}: ${msg.content}\n`;
            
            // Include any attachments
            if (msg.attachments.size > 0) {
                msg.attachments.forEach(attachment => {
                    transcriptText += `[Attachment: ${attachment.url}]\n`;
                });
            }
            
            transcriptText += '\n';
        }
    });
    
    return transcriptText; // Return the string directly
}

function createCloseEmbed(interaction) {
    return new EmbedBuilder()
        .setColor('#02a0dd')
        .setTitle('Ticket Closed')
        .setDescription(`Ticket has been closed.\n\n**Opened by:** ${interaction.user.tag}\n**Opened at:** ${interaction.channel.createdAt}\n**Closed at:** ${new Date()}`)
        .setFooter({ text: `Ticket closed by: ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) });
}

async function logTicketCreation(interaction, logChannelId, ticketChannel, reason) {
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
        const logEmbed = new EmbedBuilder()
            .setColor('#02a0dd')
            .setTitle('Ticket Opened')
            .setDescription(`A new ticket has been opened by <@${interaction.user.id}> in <#${ticketChannel.id}>.\nReason: ${reason}`)
            .setTimestamp();

        await logChannel.send({ embeds: [logEmbed] });
    }
}

async function logTicketClose(interaction, logChannelId, ticketChannel) {
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
        const logEmbed = new EmbedBuilder()
            .setColor('#02a0dd')
            .setTitle('Ticket Closed')
            .setDescription(`Ticket closed by <@${interaction.user.id}> in <#${ticketChannel.id}>.\n**Opened by:** ${interaction.user.tag}\n**Opened at:** ${ticketChannel.createdAt}\n**Closed at:** ${new Date()}`)
            .setTimestamp();

        await logChannel.send({ embeds: [logEmbed] });
    }
};

// Add this function to generate ticket numbers
async function getNextTicketNumber(guild) {
    try {
        // Get all channels that might be tickets
        const ticketChannels = guild.channels.cache.filter(channel => 
            channel.name.startsWith('member-report-') || 
            channel.name.startsWith('staff-support-') || 
            channel.name.startsWith('partnership-')
        );
        
        // Start with ticket number 1
        let highestNumber = 0;
        
        // Find the highest ticket number
        ticketChannels.forEach(channel => {
            const match = channel.name.match(/-(\d+)$/);
            if (match && match[1]) {
                const num = parseInt(match[1]);
                if (!isNaN(num) && num > highestNumber) {
                    highestNumber = num;
                }
            }
        });
        
        // Return the next number
        return highestNumber + 1;
    } catch (error) {
        console.error('Error getting next ticket number:', error);
        // Fallback to a timestamp-based number if there's an error
        return Math.floor(Date.now() / 1000) % 10000;
    }
}
