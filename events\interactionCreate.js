module.exports = {
    name: "interactionCreate",
    execute: async (interaction, client) => {
        try {
            if (!interaction.isCommand()) return;

            const command = client.commands.get(interaction.commandName);
            if (!command) return;

            await command.execute(interaction, client);
        } catch (error) {
            console.error('Error handling interaction:', error);
            
            // Respond to the user if we haven't already
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ 
                    content: 'There was an error executing this command!', 
                    ephemeral: true 
                });
            } else {
                await interaction.reply({ 
                    content: 'There was an error executing this command!', 
                    ephemeral: true 
                });
            }
        }
    },
};

