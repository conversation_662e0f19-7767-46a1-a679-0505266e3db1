const { Events, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const path = require('path');
const fs = require('fs');
const unbelievaboatAPI = require('../utils/unbelievaboat');

// Cache for user data to avoid frequent file system reads
const userDataCache = {
    tickets: new Map(),
    vehicles: new Map()
};

// Helper to load user data from JSON files
async function loadUserData(userId, dataType) {
    try {
        const dataPath = path.join(__dirname, '..', 'data', dataType === 'vehicles' ? 'vehicleData' : 'tickets', `${userId}.json`);
        if (!fs.existsSync(dataPath)) return [];

        const raw = fs.readFileSync(dataPath, 'utf8');
        const parsed = JSON.parse(raw);

        if (dataType === 'vehicles') {
            if (parsed && parsed.items && Array.isArray(parsed.items)) return parsed.items;
            if (Array.isArray(parsed)) return parsed;
            return [];
        }

        if (Array.isArray(parsed)) return parsed;

        return [];
    } catch (err) {
        console.error(`Error loading ${dataType} data for user ${userId}:`, err);
        return [];
    }
}

const createPaginationButtons = (userId, currentPage, totalPages, type) => {
    const buttons = [
        new ButtonBuilder().setCustomId(`first_${type}_${userId}_${currentPage}`).setLabel('First').setStyle(ButtonStyle.Primary).setDisabled(currentPage === 0),
        new ButtonBuilder().setCustomId(`prev_${type}_${userId}_${currentPage}`).setLabel('Previous').setStyle(ButtonStyle.Secondary).setDisabled(currentPage === 0),
        new ButtonBuilder().setCustomId(`next_${type}_${userId}_${currentPage}`).setLabel('Next').setStyle(ButtonStyle.Secondary).setDisabled(currentPage >= totalPages - 1),
        new ButtonBuilder().setCustomId(`last_${type}_${userId}_${currentPage}`).setLabel('Last').setStyle(ButtonStyle.Primary).setDisabled(currentPage >= totalPages - 1)
    ];
    return new ActionRowBuilder().addComponents(buttons);
};

async function createHeaderEmbed(interaction, userId, type, page, totalPages, totalItems) {
    const user = await interaction.guild.members.fetch(userId).catch(() => null);
    const username = user ? user.displayName : 'User';
    const avatarURL = user ? user.displayAvatarURL() : null;

    const title = type === 'registration' ? 'Vehicle Registrations' : 'Ticket Records';
    const totalText = type === 'registration' ? `Total vehicles: ${totalItems}` : `Total entries: ${totalItems}`;

    return new EmbedBuilder()
        .setColor('#626CE3')
        .setAuthor({ name: username, iconURL: avatarURL })
        .setTitle(title)
        .setDescription(`Page ${page + 1} of ${totalPages} — ${totalText}`);
}

async function createVehicleEmbed(vehicleData, page) {
    const itemsPerPage = 4;
    const totalPages = Math.ceil(vehicleData.length / itemsPerPage);

    const pageVehicles = vehicleData.slice(page * itemsPerPage, (page + 1) * itemsPerPage);

    return pageVehicles.map((v, idx) => {
        const registeredDate = v.registeredAt ? new Date(v.registeredAt).toLocaleDateString() : 'Unknown date';
        return new EmbedBuilder()
            .setColor('#626CE3')
            .setTitle(`Registered Vehicle #${page * itemsPerPage + idx + 1}`)
            .setDescription([
                `**Vehicle:** ${v.year} ${v.make} ${v.model}`,
                `**Color:** ${v.color}`,
                `**Plate:** ${v.numberPlate}`,
                `**Registered On:** ${registeredDate}`
            ].join('\n'))
            .setFooter({ text: `Page ${page + 1} of ${totalPages}` });
    });
}

async function createTicketEmbed(tickets = [], page = 0) {
    const itemsPerPage = 4;
    const totalPages = Math.ceil((Array.isArray(tickets) ? tickets.length : 0) / itemsPerPage);

    if (!Array.isArray(tickets)) {
        return [new EmbedBuilder().setColor('#626CE3').setDescription('Error loading tickets.')];
    }

    const pageTickets = tickets.slice(page * itemsPerPage, (page + 1) * itemsPerPage);

    return pageTickets.map((t, idx) => {
        const date = new Date(t.date).toLocaleString();
        const price = Number(t.price).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
        return new EmbedBuilder()
            .setColor('#626CE3')
            .setTitle(`Ticket #${page * itemsPerPage + idx + 1}`)
            .addFields(
                { name: 'Offense', value: t.offense, inline: false },
                { name: 'Fine', value: `$${price}`, inline: true },
                { name: 'Issued', value: date, inline: true },
                { name: 'Issued By', value: t.issuedBy ? `<@${t.issuedBy}>` : 'Unknown', inline: false }
            )
            .setFooter({ text: `Page ${page + 1} of ${totalPages}` });
    });
}

module.exports = {
    name: Events.InteractionCreate,
    userDataCache,
    async execute(interaction) {
        if (!interaction.isButton()) return;

        const customId = interaction.customId;
        const validPrefix = customId.startsWith('show_') || customId.startsWith('show_balance_') ||
            customId.includes('_registration_') || customId.includes('_ticket_') ||
            customId.startsWith('first_') || customId.startsWith('last_') ||
            customId.startsWith('prev_') || customId.startsWith('next_');

        if (!validPrefix) return;

        try {
            const parts = customId.split('_');
            const action = parts[0];
            const type = parts[1];
            const userId = parts[2];
            const currentPage = parts[3] ? parseInt(parts[3], 10) : 0;

            if (customId.startsWith('show_balance_')) {
                await interaction.deferReply({ ephemeral: true });
                const balance = await unbelievaboatAPI.getBalance(interaction.guild.id, userId);
                if (balance.error) {
                    await interaction.editReply({ content: 'Unable to fetch balance info.', ephemeral: true });
                    return;
                }

                const embed = new EmbedBuilder()
                    .setColor('#626CE3')
                    .setTitle('Balance Information')
                    .addFields(
                        { name: 'Wallet', value: `$${balance.wallet.toLocaleString()}`, inline: true },
                        { name: 'Bank', value: `$${balance.bank.toLocaleString()}`, inline: true },
                        { name: 'Total', value: `$${balance.total.toLocaleString()}`, inline: true }
                    )
                    .setTimestamp();

                await interaction.editReply({ embeds: [embed], ephemeral: true });
                return;
            }

            if (userDataCache.tickets.has(userId)) userDataCache.tickets.delete(userId);
            if (userDataCache.vehicles.has(userId)) userDataCache.vehicles.delete(userId);

            if (action === 'show') {
                if (type === 'registrations') {
                    const vehicleData = await loadUserData(userId, 'vehicles');

                    if (vehicleData.length === 0) {
                        const noVehiclesEmbed = new EmbedBuilder()
                            .setColor('#626CE3')
                            .setDescription('No vehicles have been registered by this user. Use the </register:1360700061759049789> command to register a vehicle.');

                        await interaction.reply({
                            embeds: [noVehiclesEmbed],
                            ephemeral: true
                        });
                        return;
                    }

                    const totalPages = Math.max(1, Math.ceil(vehicleData.length / 4));
                    const headerEmbed = await createHeaderEmbed(interaction, userId, 'registration', 0, totalPages, vehicleData.length);
                    const vehicleEmbeds = await createVehicleEmbed(vehicleData, 0);

                    await interaction.reply({
                        embeds: [headerEmbed, ...vehicleEmbeds],
                        components: [createPaginationButtons(userId, 0, totalPages, 'registration')],
                        ephemeral: true
                    });
                } else if (type === 'tickets') {
                    const tickets = await loadUserData(userId, 'tickets');

                    if (tickets.length === 0) {
                        const noTicketsEmbed = new EmbedBuilder()
                            .setColor('#626CE3')
                            .setDescription('No tickets have been given to this user.');
                        await interaction.reply({ embeds: [noTicketsEmbed], ephemeral: true });
                        return;
                    }

                    const totalPages = Math.max(1, Math.ceil(tickets.length / 4));
                    const headerEmbed = await createHeaderEmbed(interaction, userId, 'ticket', 0, totalPages, tickets.length);
                    const ticketEmbeds = await createTicketEmbed(tickets, 0);

                    await interaction.reply({
                        embeds: [headerEmbed, ...ticketEmbeds],
                        components: [createPaginationButtons(userId, 0, totalPages, 'ticket')],
                        ephemeral: true
                    });
                }
            } else if (['first', 'prev', 'next', 'last'].includes(action)) {
                await interaction.deferUpdate();

                let data, totalPages;
                const itemsPerPage = 4;

                if (type === 'registration') {
                    data = await loadUserData(userId, 'vehicles');
                } else if (type === 'ticket') {
                    data = await loadUserData(userId, 'tickets');
                } else return;

                totalPages = Math.max(1, Math.ceil(data.length / itemsPerPage));

                let newPage;
                switch (action) {
                    case 'first': newPage = 0; break;
                    case 'last': newPage = totalPages - 1; break;
                    case 'prev': newPage = Math.max(currentPage - 1, 0); break;
                    case 'next': newPage = Math.min(currentPage + 1, totalPages - 1); break;
                    default: newPage = currentPage;
                }

                const headerEmbed = await createHeaderEmbed(interaction, userId, type, newPage, totalPages, data.length);
                const embeds = type === 'registration'
                    ? await createVehicleEmbed(data, newPage)
                    : await createTicketEmbed(data, newPage);

                await interaction.editReply({
                    embeds: [headerEmbed, ...embeds],
                    components: [createPaginationButtons(userId, newPage, totalPages, type)]
                });
            }
        } catch (error) {
            console.error('Error handling button interaction:', error);
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ content: 'An error occurred.', ephemeral: true });
            } else {
                await interaction.followUp({ content: 'An error occurred.', ephemeral: true });
            }
        }
    }
};
