const { InteractionType } = require('discord.js');
const fs = require('fs');
const path = require('path');
const sessionLinkFile = path.join(__dirname, '../../data/sessionLink.json');
const startupFile = path.join(__dirname, '../../data/startup.json');

module.exports = {
  name: 'releaseButton',
  execute: async (interaction) => {
    if (interaction.type !== InteractionType.MessageComponent) return;

    if (interaction.customId === 'session_link') {
      await interaction.deferUpdate();

      // Check if the user has reacted to the startup message
      try {
        // Read startup data
        const startupData = JSON.parse(fs.readFileSync(startupFile, 'utf-8'));
        const startupMessage = await interaction.channel.messages.fetch(startupData.messageId);
        
        // Get the checkmark reaction
        const reaction = startupMessage.reactions.cache.get('✅');
        if (!reaction) {
          await interaction.followUp({
            content: `Please react to the startup message to have access to the session link. React [here](${startupMessage.url}).`,
            ephemeral: true
          });
          return;
        }

        // Check if the user has reacted
        const userReacted = await reaction.users.fetch().then(users => users.has(interaction.user.id));
        if (!userReacted) {
          await interaction.followUp({
            content: `Please react to the startup message to have access to the session link. React [here](${startupMessage.url}).`,
            ephemeral: true
          });
          return;
        }

        // If we get here, the user has reacted, so we can show the link
        let sessionLink;
        try {
          sessionLink = JSON.parse(fs.readFileSync(sessionLinkFile, 'utf-8')).link;
        } catch (err) {
          await interaction.followUp({
            content: 'No session link available at the moment.',
            ephemeral: true
          });
          return;
        }

        await interaction.followUp({
          content: `**Session Link:** ${sessionLink}`,
          ephemeral: true
        });

      } catch (error) {
        console.error('Error checking reactions:', error);
        await interaction.followUp({
          content: 'There was an error processing your request. Please try again later.',
          ephemeral: true
        });
      }
    }
  }
};
