{"name": "sfrcc", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "sfrcc", "version": "1.0.0", "license": "ISC", "dependencies": {"discord.js": "^14.15.3", "dotenv": "^16.4.5"}}, "node_modules/@discordjs/builders": {"version": "1.8.2", "resolved": "https://registry.npmjs.org/@discordjs/builders/-/builders-1.8.2.tgz", "integrity": "sha512-6wvG3QaCjtMu0xnle4SoOIeFB4y6fKMN6WZfy3BMKJdQQtPLik8KGzDwBVL/+wTtcE/ZlFjgEk74GublyEVZ7g==", "dependencies": {"@discordjs/formatters": "^0.4.0", "@discordjs/util": "^1.1.0", "@sapphire/shapeshift": "^3.9.7", "discord-api-types": "0.37.83", "fast-deep-equal": "^3.1.3", "ts-mixer": "^6.0.4", "tslib": "^2.6.2"}, "engines": {"node": ">=16.11.0"}, "funding": {"url": "https://github.com/discordjs/discord.js?sponsor"}}, "node_modules/@discordjs/collection": {"version": "1.5.3", "resolved": "https://registry.npmjs.org/@discordjs/collection/-/collection-1.5.3.tgz", "integrity": "sha512-SVb428OMd3WO1paV3rm6tSjM4wC+Kecaa1EUGX7vc6/fddvw/6lg90z4QtCqm21zvVe92vMMDt9+DkIvjXImQQ==", "engines": {"node": ">=16.11.0"}}, "node_modules/@discordjs/formatters": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/@discordjs/formatters/-/formatters-0.4.0.tgz", "integrity": "sha512-fJ06TLC1NiruF35470q3Nr1bi95BdvKFAF+T5bNfZJ4bNdqZ3VZ+Ttg6SThqTxm6qumSG3choxLBHMC69WXNXQ==", "dependencies": {"discord-api-types": "0.37.83"}, "engines": {"node": ">=16.11.0"}, "funding": {"url": "https://github.com/discordjs/discord.js?sponsor"}}, "node_modules/@discordjs/rest": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/@discordjs/rest/-/rest-2.3.0.tgz", "integrity": "sha512-C1k<PERSON>JK8aSYRv3ZwMG8cvrrW4GN0g5eMdP8AuN8ODH5DyOCbHgJspze1my3xHOAgwLJdKUbWNVyAeJ9cEdduqIg==", "dependencies": {"@discordjs/collection": "^2.1.0", "@discordjs/util": "^1.1.0", "@sapphire/async-queue": "^1.5.2", "@sapphire/snowflake": "^3.5.3", "@vladfrangu/async_event_emitter": "^2.2.4", "discord-api-types": "0.37.83", "magic-bytes.js": "^1.10.0", "tslib": "^2.6.2", "undici": "6.13.0"}, "engines": {"node": ">=16.11.0"}, "funding": {"url": "https://github.com/discordjs/discord.js?sponsor"}}, "node_modules/@discordjs/rest/node_modules/@discordjs/collection": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@discordjs/collection/-/collection-2.1.0.tgz", "integrity": "sha512-mLcTACtXUuVgutoznkh6hS3UFqYirDYAg5Dc1m8xn6OvPjetnUlf/xjtqnnc47OwWdaoCQnHmHh9KofhD6uRqw==", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/discordjs/discord.js?sponsor"}}, "node_modules/@discordjs/util": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@discordjs/util/-/util-1.1.0.tgz", "integrity": "sha512-IndcI5hzlNZ7GS96RV3Xw1R2kaDuXEp7tRIy/KlhidpN/BQ1qh1NZt3377dMLTa44xDUNKT7hnXkA/oUAzD/lg==", "engines": {"node": ">=16.11.0"}, "funding": {"url": "https://github.com/discordjs/discord.js?sponsor"}}, "node_modules/@discordjs/ws": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@discordjs/ws/-/ws-1.1.1.tgz", "integrity": "sha512-PZ+vLpxGCRtmr2RMkqh8Zp+BenUaJqlS6xhgWKEZcgC/vfHLEzpHtKkB0sl3nZWpwtcKk6YWy+pU3okL2I97FA==", "dependencies": {"@discordjs/collection": "^2.1.0", "@discordjs/rest": "^2.3.0", "@discordjs/util": "^1.1.0", "@sapphire/async-queue": "^1.5.2", "@types/ws": "^8.5.10", "@vladfrangu/async_event_emitter": "^2.2.4", "discord-api-types": "0.37.83", "tslib": "^2.6.2", "ws": "^8.16.0"}, "engines": {"node": ">=16.11.0"}, "funding": {"url": "https://github.com/discordjs/discord.js?sponsor"}}, "node_modules/@discordjs/ws/node_modules/@discordjs/collection": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@discordjs/collection/-/collection-2.1.0.tgz", "integrity": "sha512-mLcTACtXUuVgutoznkh6hS3UFqYirDYAg5Dc1m8xn6OvPjetnUlf/xjtqnnc47OwWdaoCQnHmHh9KofhD6uRqw==", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/discordjs/discord.js?sponsor"}}, "node_modules/@sapphire/async-queue": {"version": "1.5.3", "resolved": "https://registry.npmjs.org/@sapphire/async-queue/-/async-queue-1.5.3.tgz", "integrity": "sha512-x7zadcfJGxFka1Q3f8gCts1F0xMwCKbZweM85xECGI0hBTeIZJGGCrHgLggihBoprlQ/hBmDR5LKfIPqnmHM3w==", "engines": {"node": ">=v14.0.0", "npm": ">=7.0.0"}}, "node_modules/@sapphire/shapeshift": {"version": "3.9.7", "resolved": "https://registry.npmjs.org/@sapphire/shapeshift/-/shapeshift-3.9.7.tgz", "integrity": "sha512-4It2mxPSr4OGn4HSQWGmhFMsNFGfFVhWeRPCRwbH972Ek2pzfGRZtb0pJ4Ze6oIzcyh2jw7nUDa6qGlWofgd9g==", "dependencies": {"fast-deep-equal": "^3.1.3", "lodash": "^4.17.21"}, "engines": {"node": ">=v16"}}, "node_modules/@sapphire/snowflake": {"version": "3.5.3", "resolved": "https://registry.npmjs.org/@sapphire/snowflake/-/snowflake-3.5.3.tgz", "integrity": "sha512-jjmJywLAFoWeBi1W7994zZyiNWPIiqRRNAmSERxyg93xRGzNYvGjlZ0gR6x0F4gPRi2+0O6S71kOZYyr3cxaIQ==", "engines": {"node": ">=v14.0.0", "npm": ">=7.0.0"}}, "node_modules/@types/node": {"version": "20.14.12", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.14.12.tgz", "integrity": "sha512-r7wNXakLeSsGT0H1AU863vS2wa5wBOK4bWMjZz2wj+8nBx+m5PeIn0k8AloSLpRuiwdRQZwarZqHE4FNArPuJQ==", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@types/ws": {"version": "8.5.11", "resolved": "https://registry.npmjs.org/@types/ws/-/ws-8.5.11.tgz", "integrity": "sha512-4+q7P5h3SpJxaBft0Dzpbr6lmMaqh0Jr2tbhJZ/luAwvD7ohSCniYkwz/pLxuT2h0EOa6QADgJj1Ko+TzRfZ+w==", "dependencies": {"@types/node": "*"}}, "node_modules/@vladfrangu/async_event_emitter": {"version": "2.4.4", "resolved": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.4.tgz", "integrity": "sha512-ZL62PFXEIeGUI8btfJ5S8Flc286eU1ZUSjwyFQtIGXfRUDPZKO+CDJMYb1R71LjGWRZ4n202O+a6FGjsgTw58g==", "engines": {"node": ">=v14.0.0", "npm": ">=7.0.0"}}, "node_modules/discord-api-types": {"version": "0.37.83", "resolved": "https://registry.npmjs.org/discord-api-types/-/discord-api-types-0.37.83.tgz", "integrity": "sha512-urGGYeWtWNYMKnYlZnOnDHm8fVRffQs3U0SpE8RHeiuLKb/u92APS8HoQnPTFbnXmY1vVnXjXO4dOxcAn3J+DA=="}, "node_modules/discord.js": {"version": "14.15.3", "resolved": "https://registry.npmjs.org/discord.js/-/discord.js-14.15.3.tgz", "integrity": "sha512-/UJDQO10VuU6wQPglA4kz2bw2ngeeSbogiIPx/TsnctfzV/tNf+q+i1HlgtX1OGpeOBpJH9erZQNO5oRM2uAtQ==", "dependencies": {"@discordjs/builders": "^1.8.2", "@discordjs/collection": "1.5.3", "@discordjs/formatters": "^0.4.0", "@discordjs/rest": "^2.3.0", "@discordjs/util": "^1.1.0", "@discordjs/ws": "^1.1.1", "@sapphire/snowflake": "3.5.3", "discord-api-types": "0.37.83", "fast-deep-equal": "3.1.3", "lodash.snakecase": "4.1.1", "tslib": "2.6.2", "undici": "6.13.0"}, "engines": {"node": ">=16.11.0"}, "funding": {"url": "https://github.com/discordjs/discord.js?sponsor"}}, "node_modules/dotenv": {"version": "16.4.5", "resolved": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.5.tgz", "integrity": "sha512-ZmdL2rui+eB2YwhsWzjInR8LldtZHGDoQ1ugH85ppHKwpUHL7j7rN0Ti9NCnGiQbhaZ11FpR+7ao1dNsmduNUg==", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="}, "node_modules/lodash.snakecase": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz", "integrity": "sha512-QZ1d4xoBHYUeuouhEq3lk3Uq7ldgyFXGBhg04+oRLnIz8o9T65Eh+8YdroUwn846zchkA9yDsDl5CVVaV2nqYw=="}, "node_modules/magic-bytes.js": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.10.0.tgz", "integrity": "sha512-/k20Lg2q8LE5xiaaSkMXk4sfvI+9EGEykFS4b0CHHGWqDYU0bGUFSwchNOMA56D7TCs9GwVTkqe9als1/ns8UQ=="}, "node_modules/ts-mixer": {"version": "6.0.4", "resolved": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-6.0.4.tgz", "integrity": "sha512-ufKpbmrugz5Aou4wcr5Wc1UUFWOLhq+Fm6qa6P0w0K5Qw2yhaUoiWszhCVuNQyNwrlGiscHOmqYoAox1PtvgjA=="}, "node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/undici": {"version": "6.13.0", "resolved": "https://registry.npmjs.org/undici/-/undici-6.13.0.tgz", "integrity": "sha512-Q2rtqmZWrbP8nePMq7mOJIN98M0fYvSgV89vwl/BQRT4mDOeY2GXZngfGpcBBhtky3woM7G24wZV3Q304Bv6cw==", "engines": {"node": ">=18.0"}}, "node_modules/undici-types": {"version": "5.26.5", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA=="}, "node_modules/ws": {"version": "8.18.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.18.0.tgz", "integrity": "sha512-8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}}}