const { Events } = require('discord.js');
const { removeSpecificSession, removeAllSessionsByType, handleRemovalResponse } = require('../commands/utility/sessionlogremove.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isStringSelectMenu()) return;
        
        if (!interaction.customId.startsWith('remove_session_')) return;

        const [_, __, userId, type] = interaction.customId.split('_');
        const selectedValue = interaction.values[0];

        if (selectedValue === 'all') {
            const result = await removeAllSessionsByType(userId, type);
            await handleRemovalResponse(interaction, result, interaction.guild.members.cache.get(userId).user, type);
        } else {
            const result = await removeSpecificSession(userId, parseInt(selectedValue));
            await handleRemovalResponse(interaction, result, interaction.guild.members.cache.get(userId).user, type);
        }
    }
};
