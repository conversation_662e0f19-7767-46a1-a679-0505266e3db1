const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonStyle } = require('discord.js');
const axios = require('axios');
const path = require('path');
const fs = require('fs');

function countSessionsByType(userId) {
    const sessionLogsPath = path.join(__dirname, '../../data/sessionLogs');
    const filePath = path.join(sessionLogsPath, `${userId}.json`);
    
    try {
        if (!fs.existsSync(filePath)) {
            return { hosts: 0, cohosts: 0 };
        }
        
        const sessions = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        return {
            hosts: sessions.filter(log => log.hostType === 'Host').length,
            cohosts: sessions.filter(log => log.hostType === 'Co-host').length
        };
    } catch (error) {
        console.error('Error counting sessions:', error);
        return { hosts: 0, cohosts: 0 };
    }
}

const ROLE_QUOTAS = {
    '1314078918571069450': 3.0, // 3 points required
    '1314078908865577031': 3.0, // 3 points required
    '1360598598114345020': 2.0, // 2 points required
};

function calculateQuotaStatus(member, sessionCounts) {
    // Calculate total points (2 point per host, 1 points per co-host)
    const totalPoints = sessionCounts.hosts * 2 + (sessionCounts.cohosts * 1);
    
    // Find the highest quota role the member has
    let highestQuota = 0;
    let relevantRoleId = null;
    
    for (const [roleId, quota] of Object.entries(ROLE_QUOTAS)) {
        if (member.roles.cache.has(roleId) && quota > highestQuota) {
            highestQuota = quota;
            relevantRoleId = roleId;
        }
    }
    
    if (!relevantRoleId) {
        return { status: 'N/A', required: 0, current: totalPoints };
    }
    
    return {
        status: totalPoints >= highestQuota ? 'Passed' : 'Failed',
        required: highestQuota,
        current: totalPoints
    };
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('staffprofile')
        .setDescription('Displays staff profile information.')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Select a staff member to view their profile. If not selected, shows your profile.')),

    async execute(interaction) {
        try {
            const staffRoleId = '1362775746329841796'; // Replace with your staff role ID
            const selectedUser = interaction.options.getUser('user') || interaction.user;

            // Check if the selected user is a bot
            if (selectedUser.bot) {
                return await interaction.reply({ 
                    content: 'You cannot use this command on a discord bot!',
                    ephemeral: true 
                });
            }

            // Check if the user has staff role
            const member = await interaction.guild.members.fetch(selectedUser.id);
            if (!member.roles.cache.has(staffRoleId)) {
                return await interaction.reply({
                    content: 'This command can only be used on staff members!',
                    ephemeral: true
                });
            }

            const discordUserId = selectedUser.id;
            const guildId = interaction.guild.id;

            // Initial reply
            await interaction.reply({ content: 'Fetching staff profile information...', ephemeral: false });

            try {
                // Fetch Roblox data using Bloxlink API
                const bloxlinkApiKey = process.env.BLOXLINK;
                const bloxlinkApiUrl = `https://api.blox.link/v4/public/guilds/${guildId}/discord-to-roblox/${discordUserId}`;
                
                const bloxlinkResponse = await axios.get(bloxlinkApiUrl, {
                    headers: { 'Authorization': bloxlinkApiKey }
                });

                if (!bloxlinkResponse.data?.robloxID) {
                    await interaction.editReply({
                        content: 'No Roblox account linked. Please verify at https://blox.link/verify'
                    });
                    return;
                }

                const robloxUserId = bloxlinkResponse.data.robloxID;

                // Fetch Roblox username
                const robloxUserResponse = await axios.get(`https://users.roblox.com/v1/users/${robloxUserId}`);
                const robloxUsername = robloxUserResponse.data.name;

                // Fetch avatar thumbnail
                const thumbnailResponse = await axios.get(
                    `https://thumbnails.roblox.com/v1/users/avatar-headshot?userIds=${robloxUserId}&size=420x420&format=Png&isCircular=false`
                );
                const robloxProfilePicture = thumbnailResponse.data.data[0]?.imageUrl || '';

                const sessionCounts = countSessionsByType(discordUserId);
                const quotaStatus = calculateQuotaStatus(member, sessionCounts);
                const quotaText = quotaStatus.status === 'N/A' 
                    ? 'No quota requirement'
                    : `${quotaStatus.status} (${quotaStatus.current}/${quotaStatus.required} points)`;

                const staffProfileEmbed = new EmbedBuilder()
                    .setTitle(`${selectedUser.username}'s Staff Profile`)
                    .setDescription(`

                    Welcome to <@${discordUserId}>'s staff profile. Here, you can find your session hosts, co-hosts, and more information.

                    ** __Staff Profile Information__**

                        **Discord User:** <@${discordUserId}>
                        **Roblox Username:** [${robloxUsername}](https://www.roblox.com/users/${robloxUserId}/profile)
                        **Session Hosts:** ${sessionCounts.hosts}
                        **Session Co-hosts:** ${sessionCounts.cohosts}
                        **Quota:** ${quotaText}
                    `)
                    .setColor('#626CE3')
                    .setThumbnail(robloxProfilePicture)
                    .setFooter({
                        text: 'Greenville Roleplay Corporation',
                        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
                    })
                    .setTimestamp();

                const actionRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`show_session_hosts_${discordUserId}`)
                            .setLabel('Session Hosts')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_session_cohosts_${discordUserId}`)
                            .setLabel('Session Co-hosts')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_modlog_${discordUserId}_${interaction.user.id}`)
                            .setLabel('View Modlogs')
                            .setStyle(ButtonStyle.Secondary)
                    );

                await interaction.editReply({
                    content: null,
                    embeds: [staffProfileEmbed],
                    components: [actionRow]
                });

            } catch (error) {
                console.error('API Error:', error);
                await interaction.editReply({
                    content: 'Failed to fetch profile information. Please try again later.'
                });
            }

        } catch (error) {
            console.error('Error while executing the staff profile command:', error);
            const errorMessage = 'An error occurred while processing your request. Please try again later.';
            
            if (interaction.replied) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    }
};
