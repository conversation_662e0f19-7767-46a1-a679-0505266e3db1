const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed5')
    .setDescription('Displays emergency call format information.'),

  async execute(interaction) {
    const embedColor = '#626CE3';
    const staffRoleId = '1314078903522037802';

    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    await interaction.reply({ content: 'Embed5 Message Released!', ephemeral: true });

    const infoEmbed = new EmbedBuilder()
      .setColor(embedColor)
      .setTitle('Emergency Call Format (911)')
      .setDescription(
        'Use the format below when placing a 911 call for an active emergency requiring Law Enforcement (LEO), Fire Department (FD), or EMS response:\n\n' +
        '```911 CALL\n' +
        '**Name:** [Your roleplay name and/or username]  \n' +
        '**Service Requested:** [Law Enforcement / DOT / FD / EMS]  \n' +
        '**Description:** [Brief but relevant details about the emergency]  \n' +
        '**Location:** [Be specific; include nearby landmarks if needed]```' +
        '\n\n' +
        '**Non-Emergency Call Format (NON-E)**\n\n' +
        'For non-urgent situations (e.g., noise complaints, disabled vehicles, or delayed crime reports where the suspect is no longer present), use the following format:\n\n' +
        '```NON-EMERGENCY CALL\n' +
        '**Name:** [Your roleplay name and/or username]  \n' +
        '**Service Requested:** [Law Enforcement / WisDOT]  \n' +
        '**Description:** [Relevant details about the incident]  \n' +
        '**Location:** [Be specific; include nearby landmarks if needed]```'
      )
      .setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
      });

    await interaction.channel.send({ 
      embeds: [infoEmbed]
    });

    const logChannelId = '1334707231555452952';
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
      const logEmbed = new EmbedBuilder()
        .setTitle('Command Executed')
        .setDescription('The `/embed5` command was executed.')
        .addFields(
          { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
          { name: 'User ID', value: `${interaction.user.id}`, inline: true },
          { name: 'Channel', value: `${interaction.channel.name}`, inline: true }
        )
        .setColor(embedColor)
        .setTimestamp();

      await logChannel.send({ embeds: [logEmbed] });
    }
  },
};
