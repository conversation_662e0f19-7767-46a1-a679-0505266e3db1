const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('serverperks')
    .setDescription('Displays server perks'),
  async execute(interaction) {
    const staffRoleId = '1314078903522037802'; // Replace with your actual staff role ID

    // Check if the user has the required role
    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    const embedColor = '#626CE3'; // Replace with your desired embed color

    // Send initial ephemeral confirmation
    await interaction.reply({ content: 'Server Perks Released!', ephemeral: true });

    const embeds = [
      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Server Shop')
        .setDescription(
          ' > Gain access to all the benefits of our Permanent Options now available at significantly reduced prices along with exclusive perks just for patreon members. \n\n > Our shop offers 10+ unique options designed to elevate your roleplay experience. Whether you\'re looking to purchase in-game currency, invest in properties, or unlock access to Banned Vehicles (BVE), we’ve got everything you need to take your gameplay to the next level.'
        )
        .setFooter({
          text: 'Greenville Roleplay Corporation',
          iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
        }),
    ];

    const buttons = new ActionRowBuilder().addComponents(
  new ButtonBuilder()
    .setLabel('Roblox Group')
    .setStyle(ButtonStyle.Link)
    .setURL('https://www.roblox.com/communities/36056203/Greenville-Roleplay-Corporation#!/about')
);


    // Send the public message using channel.send since we already replied
    await interaction.channel.send({ 
      embeds, 
      components: [buttons]
    });
  },
};
