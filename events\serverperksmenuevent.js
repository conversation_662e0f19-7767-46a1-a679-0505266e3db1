const { Events, EmbedBuilder } = require('discord.js');

// Pre-define constant embeds outside the execute function
const embedColor = '#02a0dd';
const footerText = 'Southwest Florida Roleplay Hub';

// Pre-define all possible responses
const responses = {
    'boost-perks': {
        embeds: [
            new EmbedBuilder()
                .setColor(embedColor)
                .setTitle('**__1-2 Boost Perks__**')
                .setDescription('**1-2 Server Boosts:**\n\n> - Banned Vehicle Exemption\n> - Early Access\n> - Image Permissions')
                .setFooter({ text: footerText }),
            new EmbedBuilder()
                .setColor(embedColor)
                .setTitle('**__3+ Boost Perks__**')
                .setDescription('**3+ Server Boosts:**\n\n> - Ultra Banned Vehicle Exemption\n> - Banned Vehicle Exemption\n> - Early Access\n> - Image Permissions\n')
                .setFooter({ text: footerText })
        ]
    },
    'robux-perks': {
        embeds: [
            new EmbedBuilder()
                .setColor(embedColor)
                .setTitle('Robux Perks')
                .setDescription('> [BVE 100 Robux](https://www.roblox.com/catalog/114592756384407/BVE)\n\n> [Image Permissions 80 Robux](https://www.roblox.com/catalog/80781937363753/Image-Permissions)\n\n> [UBVE 250 robux](https://www.roblox.com/catalog/119076235392673/UBVE)\n\n> [Early access 100 Robux](https://www.roblox.com/catalog/99562285507961/Early-access) ')
                .setFooter({ text: footerText })
    
        ]
    },
    'usd-perks': {
        embeds: [
            new EmbedBuilder()
                .setColor(embedColor)
                .setTitle('USD Perks')
                .setDescription('> Any inquiries direct message <@1267517586057461785>. ')
                .setFooter({ text: footerText })
    
        ]
    },

};

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isStringSelectMenu() || interaction.customId !== 'serverperk') return;

        try {
            // Immediate response without deferring
            await interaction.reply({
                ...responses[interaction.values[0]],
                ephemeral: true
            });
        } catch (error) {
            console.error('Error in server perks menu handler:', error);
            await interaction.reply({
                content: 'An error occurred while processing your request.',
                ephemeral: true
            }).catch(console.error);
        }
    }
};
