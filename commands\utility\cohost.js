const { SlashCommandBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('co-host')
    .setDescription('Announces that the user is now co-hosting the session.'),
  
  async execute(interaction) {
    const staffRoleId = '1362775746329841796'; // Replace with your actual staff role ID

    if (!interaction.member.roles.cache.has(staffRoleId)) {
      await interaction.reply({ content: 'You do not have permission to use this command.', ephemeral: true });
      return;
    }

    const coHostMessage = `${interaction.user} is now co-hosting this session!`;

    // Acknowledge the interaction and respond with an ephemeral message
    await interaction.reply({ content: 'Co-host Message Released!', ephemeral: true });

    // Send the message publicly without an embed
    await interaction.channel.send(coHostMessage);
  }
};
