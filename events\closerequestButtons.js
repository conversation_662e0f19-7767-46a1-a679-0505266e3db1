const { Events, EmbedBuilder } = require('discord.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        // Only proceed if this is a button interaction
        if (!interaction.isButton()) return;

        // Check if this is one of our close request buttons
        if (!interaction.customId.includes('close_request_accept') && 
            !interaction.customId.includes('close_request_deny')) return;

        try {
            // Split the customId to get action and ticketCreatorId
            const [, , action, ticketCreatorId] = interaction.customId.split('_');

            // Only allow the ticket creator to respond
            if (interaction.user.id !== ticketCreatorId) {
                await interaction.reply({
                    content: 'Only the ticket creator can respond to close requests.',
                    ephemeral: true
                });
                return;
            }

            // Handle Accept action
            if (action === 'accept') {
                // Acknowledge the button click
                await interaction.deferReply({ ephemeral: true });

                // Send closing message
                const embed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('Ticket Closing')
                    .setDescription(`Ticket close request accepted by <@${interaction.user.id}>`)
                    .setTimestamp();

                await interaction.channel.send({ embeds: [embed] });

                // Inform the user
                await interaction.editReply({
                    content: 'Ticket will be closed in 5 seconds.',
                    ephemeral: true
                });

                // Delete the channel after 5 seconds
                setTimeout(async () => {
                    try {
                        await interaction.channel.delete();
                    } catch (error) {
                        console.error('Failed to delete channel:', error);
                    }
                }, 5000);
            }

            // Handle Deny action
            if (action === 'deny') {
                // Acknowledge the button click
                await interaction.deferReply({ ephemeral: true });

                // Send denial message
                await interaction.channel.send({
                    content: `<@${interaction.user.id}> has denied the close request.`
                });

                // Inform the user
                await interaction.editReply({
                    content: 'You have denied the close request.',
                    ephemeral: true
                });

                // Try to remove the close request message
                try {
                    await interaction.message.delete();
                } catch (error) {
                    console.error('Failed to delete close request message:', error);
                }
            }

        } catch (error) {
            console.error('Error in close request button handler:', error);
            
            // Try to respond to the interaction if we haven't already
            try {
                if (!interaction.replied) {
                    await interaction.reply({
                        content: 'An error occurred while processing your request.',
                        ephemeral: true
                    });
                } else {
                    await interaction.followUp({
                        content: 'An error occurred while processing your request.',
                        ephemeral: true
                    });
                }
            } catch (e) {
                console.error('Failed to send error message:', e);
            }
        }
    },
};



