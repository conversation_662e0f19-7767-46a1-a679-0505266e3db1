const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, Embed<PERSON>uilder, PermissionFlagsBits, MessageFlags } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Ensure the data directory exists
const dataDir = path.join(__dirname, '../../data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

const startupFile = path.join(dataDir, 'startup.json');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('startup')
        .setDescription('Send session startup information embeds')
        .addStringOption(option =>
            option.setName('reactions')
                .setDescription('Number of reactions needed for the session to start')
                .setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles),

    async execute(interaction) {
        const staffRoleId = '1362775746329841796';

        if (!interaction.member.roles.cache.has(staffRoleId)) {
            await interaction.reply({ 
                content: 'You do not have permission to use this command.', 
                flags: MessageFlags.Ephemeral 
            });
            return;
        }

        const optionReactions = interaction.options.getString('reactions');

        const embed = new EmbedBuilder()
            .setTitle('Session Startup')
            .setDescription(`> <@${interaction.user.id}> is hosting a session. For this session to commence, the host needs ** ${optionReactions}**+ reactions.

> Before reacting, ensure you have read over ⁠the information. Also make sure you have the correct roles for the vehicles you intend on driving throughout the session.`)
            .setColor('#626CE3')
            .setThumbnail('https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&')
            .setFooter({
                text: 'Greenville Roleplay Corporation',
                iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png?ex=6861b773&is=686065f3&hm=e570bd37fc382bc8544e08d001f9428d25cae237e7194e143cba3300bcabf91c&'
            });
            
        await interaction.reply({ content: 'Startup Command Executed.', ephemeral: true });
        
        try {
            const sentMessage = await interaction.channel.send({ content: '@everyone', embeds: [embed] });
            await sentMessage.react('✅');

            const startupData = {
                messageId: sentMessage.id,
                channelId: sentMessage.channel.id,
                requiredReactions: parseInt(optionReactions)
            };

            fs.writeFileSync(startupFile, JSON.stringify(startupData, null, 2));
        } catch (error) {
            console.error('Error in startup command:', error);
            await interaction.followUp({ 
                content: 'There was an error while executing the command.', 
                ephemeral: true 
            });
            return;
        }

        const logChannelId = '1381460621744406629';
        const logChannel = interaction.guild.channels.cache.get(logChannelId);
        if (logChannel) {
            const logEmbed = new EmbedBuilder()
                .setTitle('Command Executed')
                .setDescription(`The \`/startup\` command was executed.`)
                .addFields(
                    { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
                    { name: 'User ID', value: `${interaction.user.id}`, inline: true },
                    { name: 'Channel', value: `${interaction.channel.name}`, inline: true },
                    { name: 'Reactions Required', value: `${optionReactions}`, inline: false },
                )
                .setColor('#02a0dd')
                .setTimestamp();

            logChannel.send({ embeds: [logEmbed] });
        }
    }
};
