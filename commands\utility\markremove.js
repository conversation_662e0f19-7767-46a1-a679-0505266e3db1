const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('markremove')
        .setDescription('Remove a mark from a user')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to remove mark from')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('mark')
                .setDescription('Choose which mark to remove')
                .setRequired(true)
                .addChoices(
                    { name: 'Mark 1', value: 'mark1' },
                    { name: 'Mark 2', value: 'mark2' },
                    { name: 'Mark 3', value: 'mark3' }
                )),

    async execute(interaction) {
        const staffRoleId = '1314078903522037802'; // ✅ Replace with your actual staff role ID
        const markRoles = {
            'mark1': '1354221125160145056',
            'mark2': '1354221124312891422',
            'mark3': '1354221123977482471'
        };

        try {
            await interaction.deferReply({ ephemeral: true });

            if (!interaction.member.roles.cache.has(staffRoleId)) {
                return interaction.editReply('You do not have permission to use this command.');
            }

            const targetUser = interaction.options.getUser('user');
            const markType = interaction.options.getString('mark');
            const roleId = markRoles[markType];

            if (!roleId) {
                return interaction.editReply('Invalid mark selected.');
            }

            const member = await interaction.guild.members.fetch(targetUser.id).catch(() => null);
            if (!member) {
                return interaction.editReply('The specified user is not in the server.');
            }

            if (!member.roles.cache.has(roleId)) {
                return interaction.editReply(`${targetUser} does not have ${markType}.`);
            }

            await member.roles.remove(roleId).catch(() => null);

            const dmEmbed = new EmbedBuilder()
                .setTitle('GVRC | Mark Removed')
                .setColor(0x626CE3)
                .setDescription(`Your **${markType}** has been removed by ${interaction.user}. If you have any questions, please contact them.`)
                .setTimestamp()
                .setFooter({
                    text: 'Greenville Roleplay Corporation',
                    iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
                });

            await targetUser.send({ embeds: [dmEmbed] }).catch(() => null);

            const replyEmbed = new EmbedBuilder()
                .setDescription(`Successfully removed **${markType}** from ${targetUser}`)
                .setColor(0x626CE3)
                .setTimestamp()
                .setFooter({
                    text: 'Greenville Roleplay Corporation',
                    iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
                });

            await interaction.editReply({ embeds: [replyEmbed] });

        } catch (error) {
            await interaction.editReply('An error occurred while processing your request. Please try again.').catch(() => null);
        }
    },
};
