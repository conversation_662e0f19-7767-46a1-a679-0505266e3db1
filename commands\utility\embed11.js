const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed11')
    .setDescription('Displays training process information.'),

  async execute(interaction) {
    const embedColor = '#626CE3';
    const staffRoleId = '1314078903522037802';

    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    await interaction.reply({ content: 'Embed11 Message Released!', ephemeral: true });

    const infoEmbed = new EmbedBuilder()
      .setColor(embedColor)
      .setTitle('Training Process')
      .setDescription(
`To enhance the performance of our staff members, the training process has been overhauled to offer a more effective and flexible system.

Trainees are required to complete each phase within a specific timeframe. Deadlines vary depending on the phase, but all tasks must be completed before the set deadline to maintain an orderly training process.

> __**Phase 1: Exam**__  
> You will receive a Staff Document to study. Once you’ve reviewed the material, message a Staff Coordinator or higher to receive the exam link. You will be given 24 hours to complete the exam.

> __**Phase 2: Punishment Simulation**__  
> A Management member will create a mock punishment ticket. You must handle it professionally as if it were a real situation. Your approach will be evaluated.

> __**Phase 3: Co-Hosting**__  
> You will co-host a session led by a senior staff member. Your engagement and performance during the session will be assessed. Sitting idle will not count.

> __**Important Notes**__  
- After Phase 2, you will be promoted to Server Staff. (Phase 3 is currently inactive.)  
- A minimum score of **80%** is required to pass the exam. Failing results in automatic termination.  
- **Read the staff guide** — it contains everything you need to succeed.`
      )
      .setFooter({
        text: 'Greenville Roleplay Corporation',
        iconURL: 'https://cdn.discordapp.com/attachments/1201731049357590538/1388640182613311558/4619cbe84403becd000502df72469458.png'
      });

    await interaction.channel.send({ 
      embeds: [infoEmbed]
    });

    
    const logChannelId = '1334707231555452952';
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
      const logEmbed = new EmbedBuilder()
        .setTitle('Command Executed')
        .setDescription('The `/embed11` command was executed.')
        .addFields(
          { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
          { name: 'User ID', value: `${interaction.user.id}`, inline: true },
          { name: 'Channel', value: `${interaction.channel.name}`, inline: true }
        )
        .setColor(embedColor)
        .setTimestamp();

      await logChannel.send({ embeds: [logEmbed] });
    }
  },
};
